{"name": "inventory-manager", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:create": "npx sequelize-cli db:create", "db:drop": "npx sequelize-cli db:drop", "db:migrate": "npx sequelize-cli db:migrate", "db:migrate:undo": "npx sequelize-cli db:migrate:undo", "db:seed": "npx sequelize-cli db:seed:all", "db:seed:undo": "npx sequelize-cli db:seed:undo:all", "db:test": "node scripts/test-db-connection.js", "db:test-models": "node scripts/test-models.js"}, "repository": {"type": "git", "url": "git+https://github.com/HafizAbdullahUmar/inventory-manager.git"}, "keywords": ["inventory", "laptop", "bulk", "management", "express", "nodejs"], "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/Ha<PERSON>zAbdullahUmar/inventory-manager/issues"}, "homepage": "https://github.com/Ha<PERSON>zAbdullahUmar/inventory-manager#readme", "description": "Comprehensive bulk laptop inventory and sales management system for businesses importing laptops via shipping containers and selling to retailers", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "pg": "^8.16.0", "sequelize": "^6.37.7", "sequelize-cli": "^6.6.3"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^7.1.1"}}