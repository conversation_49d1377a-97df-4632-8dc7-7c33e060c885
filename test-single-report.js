const axios = require('axios');

const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjBjMWUwNGQ3LWE1MmUtNGFiZi1iOTlhLWUyNzBmMGZhODk4YyIsInVzZXJuYW1lIjoiYWRtaW4iLCJyb2xlIjoiYWRtaW4iLCJpYXQiOjE3NDk0NjgzNTcsImV4cCI6MTc0OTQ3MTk1NywiYXVkIjoiaW52ZW50b3J5LW1hbmFnZXItdXNlcnMiLCJpc3MiOiJpbnZlbnRvcnktbWFuYWdlciJ9.krC4zEXsV8JnDAX1sGMnhRxleoVn03z4liSsXq1kjM4';

async function testInventoryReport() {
  try {
    console.log('🧪 Testing Inventory Report...');
    
    const response = await axios.get('http://localhost:3000/api/reports/inventory', {
      headers: { 'Authorization': `Bearer ${testToken}` }
    });
    
    console.log('✅ Status:', response.status);
    console.log('📊 Success:', response.data.success);
    
    if (response.data.data) {
      console.log('📈 Report sections:', Object.keys(response.data.data));
      console.log('🏷️  Report type:', response.data.data.metadata?.reportType);
      
      if (response.data.data.agingAnalysis) {
        console.log('📅 Aging analysis entries:', response.data.data.agingAnalysis.length);
        console.log('📅 Sample aging data:', response.data.data.agingAnalysis.slice(0, 2));
      }
    }
    
    return true;
  } catch (error) {
    console.log('❌ Error:', error.response?.status || error.message);
    if (error.response?.data) {
      console.log('📝 Error details:', error.response.data);
    }
    return false;
  }
}

testInventoryReport();
