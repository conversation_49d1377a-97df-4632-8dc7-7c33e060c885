#!/usr/bin/env node

/**
 * Standalone script to test database connectivity
 * Usage: npm run db:test
 */

require('dotenv').config();
const { testDatabaseConnection, closeDatabaseConnection } = require('../src/utils/dbConnection');

const testConnection = async () => {
  console.log('🔍 Testing database connection...\n');
  
  try {
    const result = await testDatabaseConnection();
    
    if (result.status === 'connected') {
      console.log('✅ DATABASE CONNECTION SUCCESSFUL');
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
      console.log(`📊 Database: ${result.database}`);
      console.log(`🏠 Host: ${result.host}:${result.port}`);
      console.log(`🔧 Dialect: ${result.dialect}`);
      console.log(`⚡ Connection Time: ${result.connectionTime}`);
      console.log(`📝 PostgreSQL Version: ${result.version}`);
      console.log(`⏰ Timestamp: ${result.timestamp}`);
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
      
      process.exit(0);
    } else {
      console.log('❌ DATABASE CONNECTION FAILED');
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
      console.log(`📊 Database: ${result.database}`);
      console.log(`🏠 Host: ${result.host}:${result.port}`);
      console.log(`🔧 Dialect: ${result.dialect}`);
      console.log(`⚡ Connection Time: ${result.connectionTime}`);
      console.log(`❌ Error: ${result.error}`);
      console.log(`⏰ Timestamp: ${result.timestamp}`);
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
      
      console.log('💡 Troubleshooting Tips:');
      console.log('1. Ensure PostgreSQL is running: brew services start postgresql@14');
      console.log('2. Check your .env file has correct database credentials');
      console.log('3. Verify the database exists: createdb inventory_manager_dev');
      console.log('4. Test connection manually: psql -h localhost -U postgres -d inventory_manager_dev\n');
      
      process.exit(1);
    }
  } catch (error) {
    console.error('💥 Unexpected error during connection test:', error.message);
    process.exit(1);
  } finally {
    await closeDatabaseConnection();
  }
};

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\n🛑 Connection test interrupted');
  await closeDatabaseConnection();
  process.exit(1);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Connection test terminated');
  await closeDatabaseConnection();
  process.exit(1);
});

// Run the test
testConnection();
