const { sequelize } = require('../src/config/database');
const db = require('../src/models');

async function testModels() {
  try {
    console.log('🔍 Testing database models and associations...\n');

    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection successful');

    // Test model loading
    console.log('\n📋 Available Models:');
    Object.keys(db).forEach(modelName => {
      if (modelName !== 'sequelize' && modelName !== 'Sequelize') {
        console.log(`  - ${modelName}`);
      }
    });

    // Test associations
    console.log('\n🔗 Testing Model Associations:');
    
    // Container associations
    console.log('  Container associations:');
    console.log(`    - hasMany InventoryItem: ${!!db.Container.associations.inventoryItems}`);
    
    // Product associations
    console.log('  Product associations:');
    console.log(`    - hasMany InventoryItem: ${!!db.Product.associations.inventoryItems}`);
    
    // Customer associations
    console.log('  Customer associations:');
    console.log(`    - hasMany SalesOrder: ${!!db.Customer.associations.salesOrders}`);
    
    // InventoryItem associations
    console.log('  InventoryItem associations:');
    console.log(`    - belongsTo Product: ${!!db.InventoryItem.associations.product}`);
    console.log(`    - belongsTo Container: ${!!db.InventoryItem.associations.container}`);
    console.log(`    - hasOne OrderItem: ${!!db.InventoryItem.associations.orderItem}`);
    
    // SalesOrder associations
    console.log('  SalesOrder associations:');
    console.log(`    - belongsTo Customer: ${!!db.SalesOrder.associations.customer}`);
    console.log(`    - hasMany OrderItem: ${!!db.SalesOrder.associations.orderItems}`);
    
    // OrderItem associations
    console.log('  OrderItem associations:');
    console.log(`    - belongsTo SalesOrder: ${!!db.OrderItem.associations.salesOrder}`);
    console.log(`    - belongsTo InventoryItem: ${!!db.OrderItem.associations.inventoryItem}`);

    // Test basic CRUD operations
    console.log('\n🧪 Testing Basic CRUD Operations:');
    
    // Create a test container
    const container = await db.Container.create({
      container_number: 'TEST-001',
      arrival_date: new Date(),
      status: 'arrived',
      total_items: 10
    });
    console.log('  ✅ Container created successfully');

    // Create a test product
    const product = await db.Product.create({
      brand: 'Dell',
      model: 'Latitude 7420',
      specifications_json: {
        processor: 'Intel i7',
        ram: '16GB',
        storage: '512GB SSD'
      },
      base_price: 1200.00
    });
    console.log('  ✅ Product created successfully');

    // Create a test customer
    const customer = await db.Customer.create({
      company_name: 'Tech Solutions Inc.',
      contact_info: {
        email: '<EMAIL>',
        phone: '******-0123'
      },
      credit_limit: 50000.00,
      pricing_tier: 'premium'
    });
    console.log('  ✅ Customer created successfully');

    // Create a test inventory item
    const inventoryItem = await db.InventoryItem.create({
      product_id: product.id,
      container_id: container.id,
      serial_number: 'SN123456789',
      condition: 'new',
      status: 'available',
      cost_price: 1000.00
    });
    console.log('  ✅ InventoryItem created successfully');

    // Create a test sales order
    const salesOrder = await db.SalesOrder.create({
      customer_id: customer.id,
      order_date: new Date(),
      status: 'pending',
      total_amount: 1200.00
    });
    console.log('  ✅ SalesOrder created successfully');

    // Create a test order item
    const orderItem = await db.OrderItem.create({
      order_id: salesOrder.id,
      inventory_item_id: inventoryItem.id,
      quantity: 1,
      unit_price: 1200.00
    });
    console.log('  ✅ OrderItem created successfully');

    // Test associations with includes
    console.log('\n🔍 Testing Association Queries:');
    
    const containerWithItems = await db.Container.findByPk(container.id, {
      include: [{
        model: db.InventoryItem,
        as: 'inventoryItems',
        include: [{
          model: db.Product,
          as: 'product'
        }]
      }]
    });
    console.log(`  ✅ Container with ${containerWithItems.inventoryItems.length} inventory items loaded`);

    const orderWithDetails = await db.SalesOrder.findByPk(salesOrder.id, {
      include: [
        {
          model: db.Customer,
          as: 'customer'
        },
        {
          model: db.OrderItem,
          as: 'orderItems',
          include: [{
            model: db.InventoryItem,
            as: 'inventoryItem',
            include: [{
              model: db.Product,
              as: 'product'
            }]
          }]
        }
      ]
    });
    console.log(`  ✅ Sales order with customer and ${orderWithDetails.orderItems.length} items loaded`);

    // Clean up test data
    console.log('\n🧹 Cleaning up test data...');
    await orderItem.destroy();
    await salesOrder.destroy();
    await inventoryItem.destroy();
    await customer.destroy();
    await product.destroy();
    await container.destroy();
    console.log('  ✅ Test data cleaned up');

    console.log('\n🎉 All model tests passed successfully!');
    
  } catch (error) {
    console.error('❌ Model test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    await sequelize.close();
  }
}

// Run the test
testModels();
