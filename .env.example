# Server Configuration
PORT=3000
NODE_ENV=development
APP_VERSION=1.0.0

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=inventory_manager_dev
DB_USER=postgres
DB_PASSWORD=your_password_here

# Test Database Configuration (used when NODE_ENV=test)
TEST_DB_NAME=inventory_manager_test

# Authentication & Security
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production
JWT_EXPIRES_IN=24h
BCRYPT_ROUNDS=12

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration (production only)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Logging
LOG_LEVEL=info

# Database Connection Pool (production)
DB_POOL_MAX=10
DB_POOL_MIN=0
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000

# Redis Configuration (for report caching)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Reporting Configuration
REPORT_CACHE_TTL_DASHBOARD=300
REPORT_CACHE_TTL_INVENTORY=600
REPORT_CACHE_TTL_SALES=900
REPORT_CACHE_TTL_CONTAINERS=1800
REPORT_CACHE_TTL_CUSTOMERS=3600
REPORT_LOW_STOCK_THRESHOLD=10
REPORT_EXPORT_MAX_RECORDS=10000

# SSL Configuration (production)
DB_SSL=false
DB_SSL_REJECT_UNAUTHORIZED=true
