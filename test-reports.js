const axios = require('axios');
const jwt = require('jsonwebtoken');

// Test script for reporting endpoints
const BASE_URL = 'http://localhost:3000';

// Use the generated token from create-test-user.js
const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjBjMWUwNGQ3LWE1MmUtNGFiZi1iOTlhLWUyNzBmMGZhODk4YyIsInVzZXJuYW1lIjoiYWRtaW4iLCJyb2xlIjoiYWRtaW4iLCJpYXQiOjE3NDk0NjgzNTcsImV4cCI6MTc0OTQ3MTk1NywiYXVkIjoiaW52ZW50b3J5LW1hbmFnZXItdXNlcnMiLCJpc3MiOiJpbnZlbnRvcnktbWFuYWdlciJ9.krC4zEXsV8JnDAX1sGMnhRxleoVn03z4liSsXq1kjM4';

const headers = {
  'Authorization': `Bearer ${testToken}`,
  'Content-Type': 'application/json'
};

async function testEndpoint(endpoint, description) {
  try {
    console.log(`\n🧪 Testing: ${description}`);
    console.log(`📡 GET ${endpoint}`);
    
    const response = await axios.get(`${BASE_URL}${endpoint}`, { headers });
    
    console.log(`✅ Status: ${response.status}`);
    console.log(`📊 Response keys: ${Object.keys(response.data).join(', ')}`);
    
    if (response.data.data) {
      console.log(`📈 Data keys: ${Object.keys(response.data.data).join(', ')}`);
      
      if (response.data.data.metadata) {
        console.log(`🏷️  Report type: ${response.data.data.metadata.reportType}`);
        console.log(`⏰ Generated at: ${response.data.data.metadata.generatedAt}`);
      }
    }
    
    // Check for cache headers
    if (response.headers['x-cache']) {
      console.log(`💾 Cache: ${response.headers['x-cache']}`);
    }
    
    return true;
  } catch (error) {
    console.log(`❌ Error: ${error.response?.status || error.message}`);
    if (error.response?.data) {
      console.log(`📝 Error details: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    return false;
  }
}

async function testReportingSystem() {
  console.log('🚀 Starting Reporting System Tests');
  console.log('=' .repeat(50));
  
  const tests = [
    {
      endpoint: '/api/reports/dashboard',
      description: 'Dashboard Report (All Users)'
    },
    {
      endpoint: '/api/reports/inventory',
      description: 'Inventory Report (Admin/Manager)'
    },
    {
      endpoint: '/api/reports/sales',
      description: 'Sales Report (Admin/Manager)'
    },
    {
      endpoint: '/api/reports/containers',
      description: 'Container Report (Admin/Manager)'
    },
    {
      endpoint: '/api/reports/customers',
      description: 'Customer Report (Admin/Manager)'
    },
    {
      endpoint: '/api/reports/inventory?format=csv',
      description: 'Inventory Report (CSV Export)'
    },
    {
      endpoint: '/api/reports/sales?startDate=2024-01-01&endDate=2024-12-31',
      description: 'Sales Report with Date Range'
    },
    {
      endpoint: '/api/reports/dashboard?page=1&limit=10',
      description: 'Dashboard with Pagination'
    }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    const success = await testEndpoint(test.endpoint, test.description);
    if (success) {
      passed++;
    } else {
      failed++;
    }
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log('📊 Test Results Summary');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
  
  // Test authentication
  console.log('\n🔒 Testing Authentication');
  try {
    await axios.get(`${BASE_URL}/api/reports/dashboard`);
    console.log('❌ Authentication test failed - should require token');
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ Authentication test passed - correctly requires token');
    } else {
      console.log(`❌ Unexpected error: ${error.response?.status}`);
    }
  }
  
  // Test rate limiting (make multiple requests quickly)
  console.log('\n⚡ Testing Rate Limiting');
  const rateLimitPromises = Array(15).fill().map(() => 
    axios.get(`${BASE_URL}/api/reports/dashboard`, { headers }).catch(e => e.response)
  );
  
  const rateLimitResults = await Promise.all(rateLimitPromises);
  const rateLimited = rateLimitResults.filter(r => r?.status === 429).length;
  
  if (rateLimited > 0) {
    console.log(`✅ Rate limiting working - ${rateLimited} requests were rate limited`);
  } else {
    console.log('⚠️  Rate limiting may not be working as expected');
  }
  
  console.log('\n🎉 Reporting System Test Complete!');
}

// Run the tests
testReportingSystem().catch(console.error);
