const Joi = require('joi');

/**
 * Generic validation middleware factory
 */
const validate = (schema, property = 'body') => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req[property], {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const errorDetails = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
      }));

      return res.status(400).json({
        error: 'Validation error',
        message: 'Invalid input data',
        details: errorDetails,
        timestamp: new Date().toISOString(),
      });
    }

    // Replace the original data with validated and sanitized data
    req[property] = value;
    next();
  };
};

/**
 * User registration validation schema
 */
const registerSchema = Joi.object({
  username: Joi.string()
    .alphanum()
    .min(3)
    .max(50)
    .required()
    .messages({
      'string.alphanum': 'Username can only contain letters and numbers',
      'string.min': 'Username must be at least 3 characters long',
      'string.max': 'Username cannot exceed 50 characters',
      'any.required': 'Username is required',
    }),
  email: Joi.string()
    .email()
    .max(255)
    .required()
    .messages({
      'string.email': 'Must be a valid email address',
      'string.max': 'Email cannot exceed 255 characters',
      'any.required': 'Email is required',
    }),
  password: Joi.string()
    .min(8)
    .max(128)
    .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]'))
    .required()
    .messages({
      'string.min': 'Password must be at least 8 characters long',
      'string.max': 'Password cannot exceed 128 characters',
      'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
      'any.required': 'Password is required',
    }),
  role: Joi.string()
    .valid('admin', 'manager', 'employee')
    .optional()
    .messages({
      'any.only': 'Role must be admin, manager, or employee',
    }),
});

/**
 * User login validation schema
 */
const loginSchema = Joi.object({
  username: Joi.string()
    .required()
    .messages({
      'any.required': 'Username or email is required',
    }),
  password: Joi.string()
    .required()
    .messages({
      'any.required': 'Password is required',
    }),
});

/**
 * Forgot password validation schema
 */
const forgotPasswordSchema = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': 'Must be a valid email address',
      'any.required': 'Email is required',
    }),
});

/**
 * Reset password validation schema
 */
const resetPasswordSchema = Joi.object({
  token: Joi.string()
    .required()
    .messages({
      'any.required': 'Reset token is required',
    }),
  newPassword: Joi.string()
    .min(8)
    .max(128)
    .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]'))
    .required()
    .messages({
      'string.min': 'Password must be at least 8 characters long',
      'string.max': 'Password cannot exceed 128 characters',
      'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
      'any.required': 'New password is required',
    }),
});

/**
 * Update user validation schema
 */
const updateUserSchema = Joi.object({
  username: Joi.string()
    .alphanum()
    .min(3)
    .max(50)
    .optional()
    .messages({
      'string.alphanum': 'Username can only contain letters and numbers',
      'string.min': 'Username must be at least 3 characters long',
      'string.max': 'Username cannot exceed 50 characters',
    }),
  email: Joi.string()
    .email()
    .max(255)
    .optional()
    .messages({
      'string.email': 'Must be a valid email address',
      'string.max': 'Email cannot exceed 255 characters',
    }),
  role: Joi.string()
    .valid('admin', 'manager', 'employee')
    .optional()
    .messages({
      'any.only': 'Role must be admin, manager, or employee',
    }),
  is_active: Joi.boolean()
    .optional()
    .messages({
      'boolean.base': 'is_active must be a boolean value',
    }),
});

/**
 * Change password validation schema
 */
const changePasswordSchema = Joi.object({
  currentPassword: Joi.string()
    .required()
    .messages({
      'any.required': 'Current password is required',
    }),
  newPassword: Joi.string()
    .min(8)
    .max(128)
    .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]'))
    .required()
    .messages({
      'string.min': 'Password must be at least 8 characters long',
      'string.max': 'Password cannot exceed 128 characters',
      'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
      'any.required': 'New password is required',
    }),
});

/**
 * Query parameters validation schema for user listing
 */
const getUsersQuerySchema = Joi.object({
  page: Joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1',
    }),
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100',
    }),
  role: Joi.string()
    .valid('admin', 'manager', 'employee')
    .optional()
    .messages({
      'any.only': 'Role must be admin, manager, or employee',
    }),
  is_active: Joi.string()
    .valid('true', 'false')
    .optional()
    .messages({
      'any.only': 'is_active must be true or false',
    }),
  search: Joi.string()
    .max(100)
    .optional()
    .messages({
      'string.max': 'Search term cannot exceed 100 characters',
    }),
});

/**
 * UUID parameter validation schema
 */
const uuidParamSchema = Joi.object({
  id: Joi.string()
    .uuid()
    .required()
    .messages({
      'string.uuid': 'ID must be a valid UUID',
      'any.required': 'ID is required',
    }),
});

/**
 * Integer ID parameter validation schema
 */
const integerIdParamSchema = Joi.object({
  id: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': 'ID must be a number',
      'number.integer': 'ID must be an integer',
      'number.positive': 'ID must be positive',
      'any.required': 'ID is required',
    }),
});

/**
 * Container creation validation schema
 */
const createContainerSchema = Joi.object({
  container_number: Joi.string()
    .alphanum()
    .min(3)
    .max(50)
    .required()
    .messages({
      'string.alphanum': 'Container number can only contain letters and numbers',
      'string.min': 'Container number must be at least 3 characters long',
      'string.max': 'Container number cannot exceed 50 characters',
      'any.required': 'Container number is required',
    }),
  arrival_date: Joi.date()
    .iso()
    .max('now')
    .required()
    .messages({
      'date.base': 'Arrival date must be a valid date',
      'date.iso': 'Arrival date must be in ISO format',
      'date.max': 'Arrival date cannot be in the future',
      'any.required': 'Arrival date is required',
    }),
  status: Joi.string()
    .valid('arrived', 'processing', 'completed')
    .default('arrived')
    .messages({
      'any.only': 'Status must be one of: arrived, processing, completed',
    }),
  total_items: Joi.number()
    .integer()
    .min(0)
    .default(0)
    .messages({
      'number.base': 'Total items must be a number',
      'number.integer': 'Total items must be an integer',
      'number.min': 'Total items must be non-negative',
    }),
});

/**
 * Container update validation schema
 */
const updateContainerSchema = Joi.object({
  container_number: Joi.string()
    .alphanum()
    .min(3)
    .max(50)
    .optional()
    .messages({
      'string.alphanum': 'Container number can only contain letters and numbers',
      'string.min': 'Container number must be at least 3 characters long',
      'string.max': 'Container number cannot exceed 50 characters',
    }),
  arrival_date: Joi.date()
    .iso()
    .max('now')
    .optional()
    .messages({
      'date.base': 'Arrival date must be a valid date',
      'date.iso': 'Arrival date must be in ISO format',
      'date.max': 'Arrival date cannot be in the future',
    }),
  status: Joi.string()
    .valid('arrived', 'processing', 'completed')
    .optional()
    .messages({
      'any.only': 'Status must be one of: arrived, processing, completed',
    }),
  total_items: Joi.number()
    .integer()
    .min(0)
    .optional()
    .messages({
      'number.base': 'Total items must be a number',
      'number.integer': 'Total items must be an integer',
      'number.min': 'Total items must be non-negative',
    }),
});

/**
 * Container query parameters validation schema
 */
const getContainersQuerySchema = Joi.object({
  page: Joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1',
    }),
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100',
    }),
  status: Joi.string()
    .valid('arrived', 'processing', 'completed')
    .optional()
    .messages({
      'any.only': 'Status must be one of: arrived, processing, completed',
    }),
  container_number: Joi.string()
    .max(50)
    .optional()
    .messages({
      'string.max': 'Container number cannot exceed 50 characters',
    }),
  date_from: Joi.date()
    .iso()
    .optional()
    .messages({
      'date.base': 'Date from must be a valid date',
      'date.iso': 'Date from must be in ISO format',
    }),
  date_to: Joi.date()
    .iso()
    .optional()
    .when('date_from', {
      is: Joi.exist(),
      then: Joi.date().min(Joi.ref('date_from')),
    })
    .messages({
      'date.base': 'Date to must be a valid date',
      'date.iso': 'Date to must be in ISO format',
      'date.min': 'Date to must be after date from',
    }),
});

/**
 * Bulk import validation schema
 */
const bulkImportSchema = Joi.object({
  items: Joi.array()
    .items(
      Joi.object({
        product_id: Joi.string()
          .uuid()
          .required()
          .messages({
            'string.uuid': 'Product ID must be a valid UUID',
            'any.required': 'Product ID is required',
          }),
        serial_number: Joi.string()
          .min(1)
          .max(100)
          .required()
          .messages({
            'string.min': 'Serial number cannot be empty',
            'string.max': 'Serial number cannot exceed 100 characters',
            'any.required': 'Serial number is required',
          }),
        condition: Joi.string()
          .valid('new', 'refurbished', 'used', 'damaged')
          .default('new')
          .messages({
            'any.only': 'Condition must be one of: new, refurbished, used, damaged',
          }),
        cost_price: Joi.number()
          .positive()
          .precision(2)
          .required()
          .messages({
            'number.base': 'Cost price must be a number',
            'number.positive': 'Cost price must be positive',
            'any.required': 'Cost price is required',
          }),
      })
    )
    .min(1)
    .max(1000)
    .required()
    .messages({
      'array.min': 'At least one item is required',
      'array.max': 'Cannot import more than 1000 items at once',
      'any.required': 'Items array is required',
    }),
});

/**
 * Product creation validation schema
 */
const createProductSchema = Joi.object({
  brand: Joi.string()
    .trim()
    .min(1)
    .max(100)
    .required()
    .messages({
      'string.min': 'Brand cannot be empty',
      'string.max': 'Brand cannot exceed 100 characters',
      'any.required': 'Brand is required',
    }),
  model: Joi.string()
    .trim()
    .min(1)
    .max(100)
    .required()
    .messages({
      'string.min': 'Model cannot be empty',
      'string.max': 'Model cannot exceed 100 characters',
      'any.required': 'Model is required',
    }),
  specifications_json: Joi.object({
    cpu: Joi.string().max(200).optional(),
    ram: Joi.string().max(50).optional(),
    storage: Joi.string().max(100).optional(),
    screen_size: Joi.string().max(50).optional(),
    resolution: Joi.string().max(50).optional(),
    graphics: Joi.string().max(200).optional(),
    operating_system: Joi.string().max(100).optional(),
    weight: Joi.string().max(50).optional(),
    color: Joi.string().max(50).optional(),
    battery_life: Joi.string().max(50).optional(),
    ports: Joi.string().max(200).optional(),
    wireless: Joi.string().max(100).optional(),
    warranty: Joi.string().max(100).optional()
  })
    .pattern(/^custom_/, Joi.string().max(500))
    .optional()
    .messages({
      'object.base': 'Specifications must be a valid object',
    }),
  base_price: Joi.number()
    .positive()
    .precision(2)
    .max(999999.99)
    .required()
    .messages({
      'number.base': 'Base price must be a number',
      'number.positive': 'Base price must be positive',
      'number.max': 'Base price cannot exceed 999,999.99',
      'any.required': 'Base price is required',
    }),
  image_url: Joi.string()
    .uri()
    .max(500)
    .optional()
    .messages({
      'string.uri': 'Image URL must be a valid URL',
      'string.max': 'Image URL cannot exceed 500 characters',
    }),
});

/**
 * Product update validation schema
 */
const updateProductSchema = Joi.object({
  brand: Joi.string()
    .trim()
    .min(1)
    .max(100)
    .optional()
    .messages({
      'string.min': 'Brand cannot be empty',
      'string.max': 'Brand cannot exceed 100 characters',
    }),
  model: Joi.string()
    .trim()
    .min(1)
    .max(100)
    .optional()
    .messages({
      'string.min': 'Model cannot be empty',
      'string.max': 'Model cannot exceed 100 characters',
    }),
  specifications_json: Joi.object({
    cpu: Joi.string().max(200).optional(),
    ram: Joi.string().max(50).optional(),
    storage: Joi.string().max(100).optional(),
    screen_size: Joi.string().max(50).optional(),
    resolution: Joi.string().max(50).optional(),
    graphics: Joi.string().max(200).optional(),
    operating_system: Joi.string().max(100).optional(),
    weight: Joi.string().max(50).optional(),
    color: Joi.string().max(50).optional(),
    battery_life: Joi.string().max(50).optional(),
    ports: Joi.string().max(200).optional(),
    wireless: Joi.string().max(100).optional(),
    warranty: Joi.string().max(100).optional()
  })
    .pattern(/^custom_/, Joi.string().max(500))
    .optional()
    .messages({
      'object.base': 'Specifications must be a valid object',
    }),
  base_price: Joi.number()
    .positive()
    .precision(2)
    .max(999999.99)
    .optional()
    .messages({
      'number.base': 'Base price must be a number',
      'number.positive': 'Base price must be positive',
      'number.max': 'Base price cannot exceed 999,999.99',
    }),
  image_url: Joi.string()
    .uri()
    .max(500)
    .allow('')
    .optional()
    .messages({
      'string.uri': 'Image URL must be a valid URL',
      'string.max': 'Image URL cannot exceed 500 characters',
    }),
  price_update_reason: Joi.string()
    .max(200)
    .optional()
    .messages({
      'string.max': 'Price update reason cannot exceed 200 characters',
    }),
});

/**
 * Product query parameters validation schema
 */
const getProductsQuerySchema = Joi.object({
  page: Joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1',
    }),
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100',
    }),
  search: Joi.string()
    .max(200)
    .optional()
    .messages({
      'string.max': 'Search term cannot exceed 200 characters',
    }),
  brand: Joi.string()
    .max(100)
    .optional()
    .messages({
      'string.max': 'Brand filter cannot exceed 100 characters',
    }),
  model: Joi.string()
    .max(100)
    .optional()
    .messages({
      'string.max': 'Model filter cannot exceed 100 characters',
    }),
  min_price: Joi.number()
    .positive()
    .precision(2)
    .optional()
    .messages({
      'number.base': 'Minimum price must be a number',
      'number.positive': 'Minimum price must be positive',
    }),
  max_price: Joi.number()
    .positive()
    .precision(2)
    .optional()
    .when('min_price', {
      is: Joi.exist(),
      then: Joi.number().min(Joi.ref('min_price')),
    })
    .messages({
      'number.base': 'Maximum price must be a number',
      'number.positive': 'Maximum price must be positive',
      'number.min': 'Maximum price must be greater than minimum price',
    }),
  sort_by: Joi.string()
    .valid('brand', 'model', 'base_price', 'created_at', 'updated_at')
    .default('created_at')
    .messages({
      'any.only': 'Sort by must be one of: brand, model, base_price, created_at, updated_at',
    }),
  sort_order: Joi.string()
    .valid('asc', 'desc')
    .default('desc')
    .messages({
      'any.only': 'Sort order must be asc or desc',
    }),
  include_deleted: Joi.string()
    .valid('true', 'false')
    .default('false')
    .messages({
      'any.only': 'Include deleted must be true or false',
    }),
  specifications: Joi.string()
    .max(500)
    .optional()
    .messages({
      'string.max': 'Specifications filter cannot exceed 500 characters',
    }),
});

/**
 * Product bulk import validation schema
 */
const productBulkImportSchema = Joi.object({
  products: Joi.array()
    .items(
      Joi.object({
        brand: Joi.string()
          .trim()
          .min(1)
          .max(100)
          .required()
          .messages({
            'string.min': 'Brand cannot be empty',
            'string.max': 'Brand cannot exceed 100 characters',
            'any.required': 'Brand is required',
          }),
        model: Joi.string()
          .trim()
          .min(1)
          .max(100)
          .required()
          .messages({
            'string.min': 'Model cannot be empty',
            'string.max': 'Model cannot exceed 100 characters',
            'any.required': 'Model is required',
          }),
        specifications_json: Joi.object()
          .optional()
          .messages({
            'object.base': 'Specifications must be a valid object',
          }),
        base_price: Joi.number()
          .positive()
          .precision(2)
          .max(999999.99)
          .required()
          .messages({
            'number.base': 'Base price must be a number',
            'number.positive': 'Base price must be positive',
            'number.max': 'Base price cannot exceed 999,999.99',
            'any.required': 'Base price is required',
          }),
        image_url: Joi.string()
          .uri()
          .max(500)
          .optional()
          .messages({
            'string.uri': 'Image URL must be a valid URL',
            'string.max': 'Image URL cannot exceed 500 characters',
          }),
      })
    )
    .min(1)
    .max(500)
    .required()
    .messages({
      'array.min': 'At least one product is required',
      'array.max': 'Cannot import more than 500 products at once',
      'any.required': 'Products array is required',
    }),
});

/**
 * Inventory item creation validation schema
 */
const createInventoryItemSchema = Joi.object({
  product_id: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': 'Product ID must be a number',
      'number.integer': 'Product ID must be an integer',
      'number.positive': 'Product ID must be positive',
      'any.required': 'Product ID is required',
    }),
  container_id: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': 'Container ID must be a number',
      'number.integer': 'Container ID must be an integer',
      'number.positive': 'Container ID must be positive',
      'any.required': 'Container ID is required',
    }),
  serial_number: Joi.string()
    .trim()
    .min(1)
    .max(100)
    .required()
    .messages({
      'string.min': 'Serial number cannot be empty',
      'string.max': 'Serial number cannot exceed 100 characters',
      'any.required': 'Serial number is required',
    }),
  condition: Joi.string()
    .valid('new', 'refurbished', 'damaged', 'defective')
    .default('new')
    .messages({
      'any.only': 'Condition must be one of: new, refurbished, damaged, defective',
    }),
  status: Joi.string()
    .valid('available', 'reserved', 'sold', 'returned')
    .default('available')
    .messages({
      'any.only': 'Status must be one of: available, reserved, sold, returned',
    }),
  cost_price: Joi.number()
    .positive()
    .precision(2)
    .max(999999.99)
    .required()
    .messages({
      'number.base': 'Cost price must be a number',
      'number.positive': 'Cost price must be positive',
      'number.max': 'Cost price cannot exceed 999,999.99',
      'any.required': 'Cost price is required',
    }),
  location: Joi.string()
    .trim()
    .max(100)
    .optional()
    .messages({
      'string.max': 'Location cannot exceed 100 characters',
    }),
  warehouse: Joi.string()
    .trim()
    .max(50)
    .default('main')
    .messages({
      'string.max': 'Warehouse cannot exceed 50 characters',
    }),
  purchase_date: Joi.date()
    .iso()
    .max('now')
    .optional()
    .messages({
      'date.base': 'Purchase date must be a valid date',
      'date.iso': 'Purchase date must be in ISO format',
      'date.max': 'Purchase date cannot be in the future',
    }),
  notes: Joi.string()
    .trim()
    .max(1000)
    .optional()
    .messages({
      'string.max': 'Notes cannot exceed 1000 characters',
    }),
});

/**
 * Inventory item update validation schema
 */
const updateInventoryItemSchema = Joi.object({
  condition: Joi.string()
    .valid('new', 'refurbished', 'damaged', 'defective')
    .optional()
    .messages({
      'any.only': 'Condition must be one of: new, refurbished, damaged, defective',
    }),
  status: Joi.string()
    .valid('available', 'reserved', 'sold', 'returned')
    .optional()
    .messages({
      'any.only': 'Status must be one of: available, reserved, sold, returned',
    }),
  location: Joi.string()
    .trim()
    .max(100)
    .optional()
    .messages({
      'string.max': 'Location cannot exceed 100 characters',
    }),
  warehouse: Joi.string()
    .trim()
    .max(50)
    .optional()
    .messages({
      'string.max': 'Warehouse cannot exceed 50 characters',
    }),
  notes: Joi.string()
    .trim()
    .max(1000)
    .optional()
    .messages({
      'string.max': 'Notes cannot exceed 1000 characters',
    }),
  change_reason: Joi.string()
    .trim()
    .max(255)
    .optional()
    .messages({
      'string.max': 'Change reason cannot exceed 255 characters',
    }),
});

/**
 * Inventory query parameters validation schema
 */
const getInventoryQuerySchema = Joi.object({
  page: Joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1',
    }),
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100',
    }),
  status: Joi.string()
    .valid('available', 'reserved', 'sold', 'returned')
    .optional()
    .messages({
      'any.only': 'Status must be one of: available, reserved, sold, returned',
    }),
  condition: Joi.string()
    .valid('new', 'refurbished', 'damaged', 'defective')
    .optional()
    .messages({
      'any.only': 'Condition must be one of: new, refurbished, damaged, defective',
    }),
  warehouse: Joi.string()
    .max(50)
    .optional()
    .messages({
      'string.max': 'Warehouse cannot exceed 50 characters',
    }),
  location: Joi.string()
    .max(100)
    .optional()
    .messages({
      'string.max': 'Location cannot exceed 100 characters',
    }),
  serial_number: Joi.string()
    .max(100)
    .optional()
    .messages({
      'string.max': 'Serial number cannot exceed 100 characters',
    }),
  product_id: Joi.number()
    .integer()
    .positive()
    .optional()
    .messages({
      'number.base': 'Product ID must be a number',
      'number.integer': 'Product ID must be an integer',
      'number.positive': 'Product ID must be positive',
    }),
  container_id: Joi.number()
    .integer()
    .positive()
    .optional()
    .messages({
      'number.base': 'Container ID must be a number',
      'number.integer': 'Container ID must be an integer',
      'number.positive': 'Container ID must be positive',
    }),
  date_from: Joi.date()
    .iso()
    .optional()
    .messages({
      'date.base': 'Date from must be a valid date',
      'date.iso': 'Date from must be in ISO format',
    }),
  date_to: Joi.date()
    .iso()
    .optional()
    .when('date_from', {
      is: Joi.exist(),
      then: Joi.date().min(Joi.ref('date_from')),
    })
    .messages({
      'date.base': 'Date to must be a valid date',
      'date.iso': 'Date to must be in ISO format',
      'date.min': 'Date to must be after date from',
    }),
});

/**
 * Bulk inventory operations validation schema
 */
const bulkInventoryOperationsSchema = Joi.object({
  operation: Joi.string()
    .valid('status_update', 'location_update', 'import_from_container')
    .required()
    .messages({
      'any.only': 'Operation must be one of: status_update, location_update, import_from_container',
      'any.required': 'Operation is required',
    }),
  items: Joi.when('operation', {
    is: 'status_update',
    then: Joi.array()
      .items(Joi.object({
        id: Joi.number().integer().positive().required(),
        status: Joi.string().valid('available', 'reserved', 'sold', 'returned').required()
      }))
      .min(1)
      .max(100)
      .required(),
    otherwise: Joi.when('operation', {
      is: 'location_update',
      then: Joi.array()
        .items(Joi.object({
          id: Joi.number().integer().positive().required(),
          location: Joi.string().max(100).optional(),
          warehouse: Joi.string().max(50).optional()
        }))
        .min(1)
        .max(100)
        .required(),
      otherwise: Joi.array()
        .items(Joi.object({
          product_id: Joi.number().integer().positive().required(),
          container_id: Joi.number().integer().positive().required(),
          serial_number: Joi.string().min(1).max(100).required(),
          condition: Joi.string().valid('new', 'refurbished', 'damaged', 'defective').default('new'),
          cost_price: Joi.number().positive().precision(2).required(),
          location: Joi.string().max(100).optional(),
          warehouse: Joi.string().max(50).default('main')
        }))
        .min(1)
        .max(100)
        .required()
    })
  }).messages({
    'array.min': 'At least one item is required',
    'array.max': 'Cannot process more than 100 items at once',
    'any.required': 'Items array is required',
  }),
  change_reason: Joi.string()
    .max(255)
    .optional()
    .messages({
      'string.max': 'Change reason cannot exceed 255 characters',
    }),
});

/**
 * Barcode generation validation schema
 */
const generateBarcodeSchema = Joi.object({
  type: Joi.string()
    .valid('barcode', 'qr')
    .default('barcode')
    .messages({
      'any.only': 'Type must be either barcode or qr',
    }),
});

/**
 * Customer creation validation schema
 */
const createCustomerSchema = Joi.object({
  company_name: Joi.string()
    .trim()
    .min(1)
    .max(200)
    .required()
    .messages({
      'string.min': 'Company name cannot be empty',
      'string.max': 'Company name cannot exceed 200 characters',
      'any.required': 'Company name is required',
    }),
  contact_person: Joi.string()
    .trim()
    .min(1)
    .max(100)
    .required()
    .messages({
      'string.min': 'Contact person cannot be empty',
      'string.max': 'Contact person cannot exceed 100 characters',
      'any.required': 'Contact person is required',
    }),
  email: Joi.string()
    .email()
    .max(255)
    .required()
    .messages({
      'string.email': 'Must be a valid email address',
      'string.max': 'Email cannot exceed 255 characters',
      'any.required': 'Email is required',
    }),
  phone: Joi.string()
    .pattern(/^[\+]?[1-9][\d]{0,15}$/)
    .max(20)
    .optional()
    .messages({
      'string.pattern.base': 'Phone number must be valid (E.164 format preferred)',
      'string.max': 'Phone number cannot exceed 20 characters',
    }),
  address: Joi.object({
    street: Joi.string().max(200).optional(),
    city: Joi.string().max(100).optional(),
    state: Joi.string().max(100).optional(),
    postal_code: Joi.string().max(20).optional(),
    country: Joi.string().max(100).optional()
  })
    .optional()
    .messages({
      'object.base': 'Address must be a valid object',
    }),
  credit_limit: Joi.number()
    .min(0)
    .max(999999.99)
    .precision(2)
    .default(5000.00)
    .messages({
      'number.base': 'Credit limit must be a number',
      'number.min': 'Credit limit must be non-negative',
      'number.max': 'Credit limit cannot exceed 999,999.99',
    }),
  pricing_tier: Joi.string()
    .valid('bronze', 'silver', 'gold', 'platinum')
    .default('bronze')
    .messages({
      'any.only': 'Pricing tier must be one of: bronze, silver, gold, platinum',
    }),
  status: Joi.string()
    .valid('active', 'inactive', 'suspended')
    .default('active')
    .messages({
      'any.only': 'Status must be one of: active, inactive, suspended',
    }),
  payment_terms: Joi.number()
    .integer()
    .min(0)
    .max(365)
    .default(30)
    .messages({
      'number.base': 'Payment terms must be a number',
      'number.integer': 'Payment terms must be an integer',
      'number.min': 'Payment terms must be non-negative',
      'number.max': 'Payment terms cannot exceed 365 days',
    }),
});

/**
 * Customer update validation schema
 */
const updateCustomerSchema = Joi.object({
  company_name: Joi.string()
    .trim()
    .min(1)
    .max(200)
    .optional()
    .messages({
      'string.min': 'Company name cannot be empty',
      'string.max': 'Company name cannot exceed 200 characters',
    }),
  contact_person: Joi.string()
    .trim()
    .min(1)
    .max(100)
    .optional()
    .messages({
      'string.min': 'Contact person cannot be empty',
      'string.max': 'Contact person cannot exceed 100 characters',
    }),
  email: Joi.string()
    .email()
    .max(255)
    .optional()
    .messages({
      'string.email': 'Must be a valid email address',
      'string.max': 'Email cannot exceed 255 characters',
    }),
  phone: Joi.string()
    .pattern(/^[\+]?[1-9][\d]{0,15}$/)
    .max(20)
    .optional()
    .messages({
      'string.pattern.base': 'Phone number must be valid (E.164 format preferred)',
      'string.max': 'Phone number cannot exceed 20 characters',
    }),
  address: Joi.object({
    street: Joi.string().max(200).optional(),
    city: Joi.string().max(100).optional(),
    state: Joi.string().max(100).optional(),
    postal_code: Joi.string().max(20).optional(),
    country: Joi.string().max(100).optional()
  })
    .optional()
    .messages({
      'object.base': 'Address must be a valid object',
    }),
  credit_limit: Joi.number()
    .min(0)
    .max(999999.99)
    .precision(2)
    .optional()
    .messages({
      'number.base': 'Credit limit must be a number',
      'number.min': 'Credit limit must be non-negative',
      'number.max': 'Credit limit cannot exceed 999,999.99',
    }),
  pricing_tier: Joi.string()
    .valid('bronze', 'silver', 'gold', 'platinum')
    .optional()
    .messages({
      'any.only': 'Pricing tier must be one of: bronze, silver, gold, platinum',
    }),
  status: Joi.string()
    .valid('active', 'inactive', 'suspended')
    .optional()
    .messages({
      'any.only': 'Status must be one of: active, inactive, suspended',
    }),
  payment_terms: Joi.number()
    .integer()
    .min(0)
    .max(365)
    .optional()
    .messages({
      'number.base': 'Payment terms must be a number',
      'number.integer': 'Payment terms must be an integer',
      'number.min': 'Payment terms must be non-negative',
      'number.max': 'Payment terms cannot exceed 365 days',
    }),
});

/**
 * Customer query parameters validation schema
 */
const getCustomersQuerySchema = Joi.object({
  page: Joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1',
    }),
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(20)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100',
    }),
  search: Joi.string()
    .max(100)
    .optional()
    .messages({
      'string.max': 'Search term cannot exceed 100 characters',
    }),
  pricing_tier: Joi.string()
    .valid('bronze', 'silver', 'gold', 'platinum')
    .optional()
    .messages({
      'any.only': 'Pricing tier must be one of: bronze, silver, gold, platinum',
    }),
  status: Joi.string()
    .valid('active', 'inactive', 'suspended')
    .optional()
    .messages({
      'any.only': 'Status must be one of: active, inactive, suspended',
    }),
  credit_limit_min: Joi.number()
    .min(0)
    .optional()
    .messages({
      'number.base': 'Minimum credit limit must be a number',
      'number.min': 'Minimum credit limit must be non-negative',
    }),
  credit_limit_max: Joi.number()
    .min(0)
    .optional()
    .when('credit_limit_min', {
      is: Joi.exist(),
      then: Joi.number().min(Joi.ref('credit_limit_min')),
    })
    .messages({
      'number.base': 'Maximum credit limit must be a number',
      'number.min': 'Maximum credit limit must be greater than minimum',
    }),
});

/**
 * Customer communication creation validation schema
 */
const createCustomerCommunicationSchema = Joi.object({
  type: Joi.string()
    .valid('email', 'phone', 'meeting', 'note', 'follow_up', 'complaint', 'support')
    .required()
    .messages({
      'any.only': 'Type must be one of: email, phone, meeting, note, follow_up, complaint, support',
      'any.required': 'Communication type is required',
    }),
  subject: Joi.string()
    .trim()
    .min(1)
    .max(200)
    .required()
    .messages({
      'string.min': 'Subject cannot be empty',
      'string.max': 'Subject cannot exceed 200 characters',
      'any.required': 'Subject is required',
    }),
  content: Joi.string()
    .trim()
    .min(1)
    .max(10000)
    .required()
    .messages({
      'string.min': 'Content cannot be empty',
      'string.max': 'Content cannot exceed 10000 characters',
      'any.required': 'Content is required',
    }),
  direction: Joi.string()
    .valid('inbound', 'outbound')
    .default('outbound')
    .messages({
      'any.only': 'Direction must be either inbound or outbound',
    }),
  priority: Joi.string()
    .valid('low', 'medium', 'high', 'urgent')
    .default('medium')
    .messages({
      'any.only': 'Priority must be one of: low, medium, high, urgent',
    }),
  follow_up_required: Joi.boolean()
    .default(false)
    .messages({
      'boolean.base': 'Follow-up required must be a boolean value',
    }),
  follow_up_date: Joi.date()
    .iso()
    .min('now')
    .optional()
    .when('follow_up_required', {
      is: true,
      then: Joi.required(),
    })
    .messages({
      'date.base': 'Follow-up date must be a valid date',
      'date.iso': 'Follow-up date must be in ISO format',
      'date.min': 'Follow-up date must be in the future',
      'any.required': 'Follow-up date is required when follow-up is required',
    }),
  communication_date: Joi.date()
    .iso()
    .max('now')
    .default(() => new Date())
    .messages({
      'date.base': 'Communication date must be a valid date',
      'date.iso': 'Communication date must be in ISO format',
      'date.max': 'Communication date cannot be in the future',
    }),
});

/**
 * Customer bulk import validation schema
 */
const customerBulkImportSchema = Joi.object({
  customers: Joi.array()
    .items(
      Joi.object({
        company_name: Joi.string().trim().min(1).max(200).required(),
        contact_person: Joi.string().trim().min(1).max(100).required(),
        email: Joi.string().email().max(255).required(),
        phone: Joi.string().pattern(/^[\+]?[1-9][\d]{0,15}$/).max(20).optional(),
        credit_limit: Joi.number().min(0).max(999999.99).precision(2).default(5000.00),
        pricing_tier: Joi.string().valid('bronze', 'silver', 'gold', 'platinum').default('bronze'),
        payment_terms: Joi.number().integer().min(0).max(365).default(30)
      })
    )
    .min(1)
    .max(500)
    .required()
    .messages({
      'array.min': 'At least one customer is required',
      'array.max': 'Cannot import more than 500 customers at once',
      'any.required': 'Customers array is required',
    }),
});

// ===== SALES ORDER VALIDATION SCHEMAS =====

/**
 * Create sales order validation schema
 */
const createSalesOrderSchema = Joi.object({
  customer_id: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': 'Customer ID must be a number',
      'number.integer': 'Customer ID must be an integer',
      'number.positive': 'Customer ID must be positive',
      'any.required': 'Customer ID is required',
    }),
  items: Joi.array()
    .items(Joi.object({
      product_id: Joi.number()
        .integer()
        .positive()
        .required()
        .messages({
          'number.base': 'Product ID must be a number',
          'number.integer': 'Product ID must be an integer',
          'number.positive': 'Product ID must be positive',
          'any.required': 'Product ID is required',
        }),
      quantity: Joi.number()
        .integer()
        .min(1)
        .max(1000)
        .required()
        .messages({
          'number.base': 'Quantity must be a number',
          'number.integer': 'Quantity must be an integer',
          'number.min': 'Quantity must be at least 1',
          'number.max': 'Quantity cannot exceed 1000',
          'any.required': 'Quantity is required',
        }),
    }))
    .min(1)
    .max(100)
    .required()
    .messages({
      'array.min': 'At least one item is required',
      'array.max': 'Cannot have more than 100 items per order',
      'any.required': 'Items array is required',
    }),
  notes: Joi.string()
    .max(1000)
    .optional()
    .messages({
      'string.max': 'Notes cannot exceed 1000 characters',
    }),
});

/**
 * Update sales order status validation schema
 */
const updateOrderStatusSchema = Joi.object({
  status: Joi.string()
    .valid('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'completed', 'cancelled')
    .required()
    .messages({
      'any.only': 'Status must be one of: pending, confirmed, processing, shipped, delivered, completed, cancelled',
      'any.required': 'Status is required',
    }),
  notes: Joi.string()
    .max(500)
    .optional()
    .messages({
      'string.max': 'Notes cannot exceed 500 characters',
    }),
});

/**
 * Get sales orders query validation schema
 */
const getSalesOrdersQuerySchema = Joi.object({
  page: Joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1',
    }),
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(20)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100',
    }),
  status: Joi.string()
    .valid('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'completed', 'cancelled')
    .optional()
    .messages({
      'any.only': 'Status must be one of: pending, confirmed, processing, shipped, delivered, completed, cancelled',
    }),
  customer_id: Joi.number()
    .integer()
    .positive()
    .optional()
    .messages({
      'number.base': 'Customer ID must be a number',
      'number.integer': 'Customer ID must be an integer',
      'number.positive': 'Customer ID must be positive',
    }),
  date_from: Joi.date()
    .iso()
    .optional()
    .messages({
      'date.base': 'Date from must be a valid date',
      'date.format': 'Date from must be in ISO format',
    }),
  date_to: Joi.date()
    .iso()
    .min(Joi.ref('date_from'))
    .optional()
    .messages({
      'date.base': 'Date to must be a valid date',
      'date.format': 'Date to must be in ISO format',
      'date.min': 'Date to must be after date from',
    }),
  min_amount: Joi.number()
    .min(0)
    .precision(2)
    .optional()
    .messages({
      'number.base': 'Minimum amount must be a number',
      'number.min': 'Minimum amount must be non-negative',
    }),
  max_amount: Joi.number()
    .min(Joi.ref('min_amount'))
    .precision(2)
    .optional()
    .messages({
      'number.base': 'Maximum amount must be a number',
      'number.min': 'Maximum amount must be greater than minimum amount',
    }),
  sort_by: Joi.string()
    .valid('order_date', 'total_amount', 'status', 'customer_id')
    .default('order_date')
    .messages({
      'any.only': 'Sort by must be one of: order_date, total_amount, status, customer_id',
    }),
  sort_order: Joi.string()
    .valid('ASC', 'DESC')
    .default('DESC')
    .messages({
      'any.only': 'Sort order must be ASC or DESC',
    }),
});

/**
 * Order fulfillment validation schema
 */
const fulfillOrderSchema = Joi.object({
  shipping_info: Joi.object({
    tracking_number: Joi.string()
      .max(100)
      .optional()
      .messages({
        'string.max': 'Tracking number cannot exceed 100 characters',
      }),
    carrier: Joi.string()
      .max(50)
      .optional()
      .messages({
        'string.max': 'Carrier cannot exceed 50 characters',
      }),
    estimated_delivery: Joi.date()
      .iso()
      .min('now')
      .optional()
      .messages({
        'date.base': 'Estimated delivery must be a valid date',
        'date.format': 'Estimated delivery must be in ISO format',
        'date.min': 'Estimated delivery must be in the future',
      }),
  }).optional(),
  notes: Joi.string()
    .max(500)
    .optional()
    .messages({
      'string.max': 'Notes cannot exceed 500 characters',
    }),
});

/**
 * Generate order quote validation schema
 */
const generateQuoteSchema = Joi.object({
  customer_id: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': 'Customer ID must be a number',
      'number.integer': 'Customer ID must be an integer',
      'number.positive': 'Customer ID must be positive',
      'any.required': 'Customer ID is required',
    }),
  items: Joi.array()
    .items(Joi.object({
      product_id: Joi.number()
        .integer()
        .positive()
        .required()
        .messages({
          'number.base': 'Product ID must be a number',
          'number.integer': 'Product ID must be an integer',
          'number.positive': 'Product ID must be positive',
          'any.required': 'Product ID is required',
        }),
      quantity: Joi.number()
        .integer()
        .min(1)
        .max(1000)
        .required()
        .messages({
          'number.base': 'Quantity must be a number',
          'number.integer': 'Quantity must be an integer',
          'number.min': 'Quantity must be at least 1',
          'number.max': 'Quantity cannot exceed 1000',
          'any.required': 'Quantity is required',
        }),
    }))
    .min(1)
    .max(100)
    .required()
    .messages({
      'array.min': 'At least one item is required',
      'array.max': 'Cannot have more than 100 items per quote',
      'any.required': 'Items array is required',
    }),
});

// Export validation middleware functions
module.exports = {
  validate,
  validateRegister: validate(registerSchema),
  validateLogin: validate(loginSchema),
  validateForgotPassword: validate(forgotPasswordSchema),
  validateResetPassword: validate(resetPasswordSchema),
  validateUpdateUser: validate(updateUserSchema),
  validateChangePassword: validate(changePasswordSchema),
  validateGetUsersQuery: validate(getUsersQuerySchema, 'query'),
  validateUuidParam: validate(uuidParamSchema, 'params'),
  validateIntegerIdParam: validate(integerIdParamSchema, 'params'),
  validateCreateContainer: validate(createContainerSchema),
  validateUpdateContainer: validate(updateContainerSchema),
  validateGetContainersQuery: validate(getContainersQuerySchema, 'query'),
  validateBulkImport: validate(bulkImportSchema),
  validateCreateProduct: validate(createProductSchema),
  validateUpdateProduct: validate(updateProductSchema),
  validateGetProductsQuery: validate(getProductsQuerySchema, 'query'),
  validateProductBulkImport: validate(productBulkImportSchema),
  validateCreateInventoryItem: validate(createInventoryItemSchema),
  validateUpdateInventoryItem: validate(updateInventoryItemSchema),
  validateGetInventoryQuery: validate(getInventoryQuerySchema, 'query'),
  validateBulkInventoryOperations: validate(bulkInventoryOperationsSchema),
  validateGenerateBarcode: validate(generateBarcodeSchema),
  validateCreateCustomer: validate(createCustomerSchema),
  validateUpdateCustomer: validate(updateCustomerSchema),
  validateGetCustomersQuery: validate(getCustomersQuerySchema, 'query'),
  validateCreateCustomerCommunication: validate(createCustomerCommunicationSchema),
  validateCustomerBulkImport: validate(customerBulkImportSchema),
  // Sales order validations
  validateCreateSalesOrder: validate(createSalesOrderSchema),
  validateUpdateOrderStatus: validate(updateOrderStatusSchema),
  validateGetSalesOrdersQuery: validate(getSalesOrdersQuerySchema, 'query'),
  validateFulfillOrder: validate(fulfillOrderSchema),
  validateGenerateQuote: validate(generateQuoteSchema),
};
