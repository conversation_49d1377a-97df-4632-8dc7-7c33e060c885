/**
 * Redis-based caching middleware for reports
 * Note: This is a basic implementation. In production, you would use actual Redis.
 * For now, we'll use in-memory caching with TTL simulation.
 */

// In-memory cache simulation (replace with Redis in production)
const cache = new Map();
const cacheTimestamps = new Map();

/**
 * Generate cache key from request parameters
 */
const generateCacheKey = (req) => {
  const { path, query, user } = req;
  const keyParts = [
    path,
    JSON.stringify(query),
    user.role, // Include role for role-based caching
  ];
  return `report:${Buffer.from(keyParts.join('|')).toString('base64')}`;
};

/**
 * Check if cache entry is expired
 */
const isCacheExpired = (key, ttl) => {
  const timestamp = cacheTimestamps.get(key);
  if (!timestamp) return true;
  
  const now = Date.now();
  return (now - timestamp) > (ttl * 1000);
};

/**
 * Clean expired cache entries
 */
const cleanExpiredCache = () => {
  const now = Date.now();
  
  for (const [key, timestamp] of cacheTimestamps.entries()) {
    // Default TTL of 15 minutes for cleanup
    if ((now - timestamp) > (15 * 60 * 1000)) {
      cache.delete(key);
      cacheTimestamps.delete(key);
    }
  }
};

/**
 * Get TTL based on report type and data freshness requirements
 */
const getTTL = (reportType, isRealTime = false) => {
  if (isRealTime) {
    return 60; // 1 minute for real-time data
  }

  switch (reportType) {
    case 'dashboard':
      return 5 * 60; // 5 minutes for dashboard
    case 'inventory':
      return 10 * 60; // 10 minutes for inventory
    case 'sales':
      return 15 * 60; // 15 minutes for sales
    case 'containers':
      return 30 * 60; // 30 minutes for containers
    case 'customers':
      return 60 * 60; // 1 hour for customers
    default:
      return 15 * 60; // 15 minutes default
  }
};

/**
 * Cache middleware factory
 */
const createCacheMiddleware = (reportType, options = {}) => {
  return (req, res, next) => {
    // Skip caching for non-GET requests or if format is not JSON
    if (req.method !== 'GET' || (req.query.format && req.query.format !== 'json')) {
      return next();
    }

    // Skip caching if explicitly disabled
    if (req.query.nocache === 'true' || options.skipCache) {
      return next();
    }

    const cacheKey = generateCacheKey(req);
    const ttl = options.ttl || getTTL(reportType, options.isRealTime);

    // Check if we have cached data and it's not expired
    if (cache.has(cacheKey) && !isCacheExpired(cacheKey, ttl)) {
      const cachedData = cache.get(cacheKey);
      
      // Add cache headers
      res.setHeader('X-Cache', 'HIT');
      res.setHeader('X-Cache-TTL', ttl);
      res.setHeader('X-Cache-Key', cacheKey.substring(0, 16) + '...');
      
      return res.json({
        ...cachedData,
        metadata: {
          ...cachedData.metadata,
          cached: true,
          cacheHit: true,
          cacheTimestamp: new Date(cacheTimestamps.get(cacheKey)).toISOString()
        }
      });
    }

    // Store original res.json to intercept response
    const originalJson = res.json;
    
    res.json = function(data) {
      // Only cache successful responses
      if (res.statusCode === 200 && data && !data.error) {
        try {
          // Store in cache with timestamp
          cache.set(cacheKey, data);
          cacheTimestamps.set(cacheKey, Date.now());
          
          // Add cache headers
          res.setHeader('X-Cache', 'MISS');
          res.setHeader('X-Cache-TTL', ttl);
          res.setHeader('X-Cache-Key', cacheKey.substring(0, 16) + '...');
          
          // Add cache metadata to response
          if (data.metadata) {
            data.metadata.cached = false;
            data.metadata.cacheKey = cacheKey.substring(0, 16) + '...';
            data.metadata.cacheTTL = ttl;
          }
          
          // Clean expired entries periodically
          if (Math.random() < 0.1) { // 10% chance
            cleanExpiredCache();
          }
          
        } catch (error) {
          console.error('Cache storage error:', error);
          // Continue without caching if there's an error
        }
      }
      
      // Call original json method
      return originalJson.call(this, data);
    };

    next();
  };
};

/**
 * Invalidate cache for specific patterns
 */
const invalidateCache = (pattern) => {
  const keysToDelete = [];
  
  for (const key of cache.keys()) {
    if (key.includes(pattern)) {
      keysToDelete.push(key);
    }
  }
  
  keysToDelete.forEach(key => {
    cache.delete(key);
    cacheTimestamps.delete(key);
  });
  
  return keysToDelete.length;
};

/**
 * Clear all cache
 */
const clearCache = () => {
  const size = cache.size;
  cache.clear();
  cacheTimestamps.clear();
  return size;
};

/**
 * Get cache statistics
 */
const getCacheStats = () => {
  const now = Date.now();
  let expiredCount = 0;
  
  for (const timestamp of cacheTimestamps.values()) {
    if ((now - timestamp) > (15 * 60 * 1000)) {
      expiredCount++;
    }
  }
  
  return {
    totalEntries: cache.size,
    expiredEntries: expiredCount,
    activeEntries: cache.size - expiredCount,
    memoryUsage: process.memoryUsage(),
    lastCleanup: new Date().toISOString()
  };
};

/**
 * Middleware for cache management endpoints
 */
const cacheManagement = {
  // Get cache statistics
  getStats: (req, res) => {
    const stats = getCacheStats();
    res.json({
      success: true,
      data: stats,
      timestamp: new Date().toISOString()
    });
  },
  
  // Clear specific cache pattern
  clearPattern: (req, res) => {
    const { pattern } = req.params;
    const deletedCount = invalidateCache(pattern);
    
    res.json({
      success: true,
      message: `Cleared ${deletedCount} cache entries matching pattern: ${pattern}`,
      deletedCount,
      timestamp: new Date().toISOString()
    });
  },
  
  // Clear all cache
  clearAll: (req, res) => {
    const deletedCount = clearCache();
    
    res.json({
      success: true,
      message: `Cleared all cache entries`,
      deletedCount,
      timestamp: new Date().toISOString()
    });
  }
};

// Predefined cache middleware for different report types
const reportCacheMiddleware = {
  dashboard: createCacheMiddleware('dashboard', { ttl: 5 * 60 }),
  inventory: createCacheMiddleware('inventory', { ttl: 10 * 60 }),
  sales: createCacheMiddleware('sales', { ttl: 15 * 60 }),
  containers: createCacheMiddleware('containers', { ttl: 30 * 60 }),
  customers: createCacheMiddleware('customers', { ttl: 60 * 60 }),
  realtime: createCacheMiddleware('realtime', { ttl: 60, isRealTime: true })
};

module.exports = {
  createCacheMiddleware,
  reportCacheMiddleware,
  invalidateCache,
  clearCache,
  getCacheStats,
  cacheManagement,
  generateCacheKey,
  getTTL
};
