const morgan = require('morgan');

/**
 * Custom token for user information
 */
morgan.token('user', (req) => {
  if (req.user) {
    return `${req.user.username}(${req.user.role})`;
  }
  return 'anonymous';
});

/**
 * Custom token for request body (sanitized)
 */
morgan.token('body', (req) => {
  if (req.body && Object.keys(req.body).length > 0) {
    // Create a sanitized copy of the body
    const sanitizedBody = { ...req.body };
    
    // Remove sensitive fields
    delete sanitizedBody.password;
    delete sanitizedBody.currentPassword;
    delete sanitizedBody.newPassword;
    delete sanitizedBody.password_hash;
    delete sanitizedBody.token;
    
    return JSON.stringify(sanitizedBody);
  }
  return '';
});

/**
 * Custom token for response time in milliseconds
 */
morgan.token('response-time-ms', (req, res) => {
  if (!req._startAt || !res._startAt) {
    return '';
  }
  
  const ms = (res._startAt[0] - req._startAt[0]) * 1000 +
             (res._startAt[1] - req._startAt[1]) * 1e-6;
  
  return ms.toFixed(3);
});

/**
 * Authentication request logger
 * Logs all authentication-related requests with detailed information
 */
const authLogger = morgan(
  ':remote-addr - :user [:date[clf]] ":method :url HTTP/:http-version" :status :res[content-length] ":referrer" ":user-agent" :response-time-ms ms :body',
  {
    // Only log authentication routes
    skip: (req) => !req.originalUrl.includes('/auth'),
    stream: {
      write: (message) => {
        // In production, you might want to write to a file or external logging service
        console.log(`[AUTH] ${message.trim()}`);
      }
    }
  }
);

/**
 * User management request logger
 * Logs all user management requests
 */
const userLogger = morgan(
  ':remote-addr - :user [:date[clf]] ":method :url HTTP/:http-version" :status :res[content-length] ":referrer" ":user-agent" :response-time-ms ms',
  {
    // Only log user management routes
    skip: (req) => !req.originalUrl.includes('/users'),
    stream: {
      write: (message) => {
        console.log(`[USER] ${message.trim()}`);
      }
    }
  }
);

/**
 * Security event logger
 * Logs security-related events like failed logins, unauthorized access attempts
 */
const logSecurityEvent = (req, res, event, details = {}) => {
  const logEntry = {
    timestamp: new Date().toISOString(),
    event,
    ip: req.ip || req.connection.remoteAddress,
    userAgent: req.get('User-Agent'),
    url: req.originalUrl,
    method: req.method,
    user: req.user ? `${req.user.username}(${req.user.role})` : 'anonymous',
    ...details
  };

  console.log(`[SECURITY] ${JSON.stringify(logEntry)}`);
};

/**
 * Middleware to log failed authentication attempts
 */
const logFailedAuth = (req, res, next) => {
  const originalSend = res.send;
  
  res.send = function(data) {
    // Check if this is an authentication failure
    if (res.statusCode === 401 || res.statusCode === 403) {
      let parsedData;
      try {
        parsedData = typeof data === 'string' ? JSON.parse(data) : data;
      } catch (e) {
        parsedData = data;
      }

      if (parsedData && parsedData.error) {
        logSecurityEvent(req, res, 'AUTH_FAILURE', {
          error: parsedData.error,
          message: parsedData.message,
          statusCode: res.statusCode
        });
      }
    }

    // Call the original send method
    originalSend.call(this, data);
  };

  next();
};

/**
 * Middleware to log successful authentication
 */
const logSuccessfulAuth = (req, res, next) => {
  const originalSend = res.send;
  
  res.send = function(data) {
    // Check if this is a successful login
    if (res.statusCode === 200 || res.statusCode === 201) {
      let parsedData;
      try {
        parsedData = typeof data === 'string' ? JSON.parse(data) : data;
      } catch (e) {
        parsedData = data;
      }

      if (parsedData && parsedData.token && parsedData.user) {
        logSecurityEvent(req, res, 'AUTH_SUCCESS', {
          userId: parsedData.user.id,
          username: parsedData.user.username,
          role: parsedData.user.role,
          statusCode: res.statusCode
        });
      }
    }

    // Call the original send method
    originalSend.call(this, data);
  };

  next();
};

/**
 * Middleware to log password changes
 */
const logPasswordChange = (req, res, next) => {
  const originalSend = res.send;
  
  res.send = function(data) {
    // Check if this is a successful password change
    if (res.statusCode === 200 && req.originalUrl.includes('/password')) {
      logSecurityEvent(req, res, 'PASSWORD_CHANGE', {
        userId: req.user ? req.user.id : 'unknown',
        username: req.user ? req.user.username : 'unknown',
        statusCode: res.statusCode
      });
    }

    // Call the original send method
    originalSend.call(this, data);
  };

  next();
};

module.exports = {
  authLogger,
  userLogger,
  logSecurityEvent,
  logFailedAuth,
  logSuccessfulAuth,
  logPasswordChange,
};
