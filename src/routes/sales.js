const express = require('express');
const rateLimit = require('express-rate-limit');
const { authenticateToken, authorize } = require('../middleware/auth');
const {
  validateCreateSalesOrder,
  validateUpdateOrderStatus,
  validateGetSalesOrdersQuery,
  validateFulfillOrder,
  validateGenerateQuote,
  validateIntegerIdParam
} = require('../middleware/validation');
const {
  generateQuote,
  createSalesOrder,
  getSalesOrders,
  getSalesOrderById,
  updateOrderStatus,
  fulfillOrder
} = require('../controllers/salesController');

const router = express.Router();

// Rate limiting configurations
const salesGeneralLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many sales requests',
    message: 'Please try again later',
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const salesCreateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 20, // Limit each IP to 20 order creations per windowMs
  message: {
    error: 'Too many order creation requests',
    message: 'Please try again later',
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const quoteGenerationLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // Limit each IP to 50 quote generations per windowMs
  message: {
    error: 'Too many quote generation requests',
    message: 'Please try again later',
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const fulfillmentLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 30, // Limit each IP to 30 fulfillment requests per windowMs
  message: {
    error: 'Too many fulfillment requests',
    message: 'Please try again later',
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Apply general rate limiting to all sales routes
router.use(salesGeneralLimit);

// All routes require authentication
router.use(authenticateToken);

/**
 * @route   POST /api/orders/quote
 * @desc    Generate order quote with pricing and availability
 * @access  Admin, Manager, Employee
 * @rateLimit 50 requests per 15 minutes
 */
router.post('/quote',
  quoteGenerationLimit,
  authorize(['admin', 'manager', 'employee']),
  validateGenerateQuote,
  generateQuote
);

/**
 * @route   POST /api/orders
 * @desc    Create new sales order with inventory allocation
 * @access  Admin, Manager
 * @rateLimit 20 requests per 15 minutes
 */
router.post('/',
  salesCreateLimit,
  authorize(['admin', 'manager']),
  validateCreateSalesOrder,
  createSalesOrder
);

/**
 * @route   GET /api/orders
 * @desc    List orders with filtering by status, customer, date range, and pagination
 * @access  Admin, Manager, Employee
 * @query   page, limit, status, customer_id, date_from, date_to, min_amount, max_amount, sort_by, sort_order
 */
router.get('/',
  authorize(['admin', 'manager', 'employee']),
  validateGetSalesOrdersQuery,
  getSalesOrders
);

/**
 * @route   GET /api/orders/:id
 * @desc    Get detailed order information including line items and fulfillment history
 * @access  Admin, Manager, Employee
 */
router.get('/:id',
  authorize(['admin', 'manager', 'employee']),
  validateIntegerIdParam,
  getSalesOrderById
);

/**
 * @route   PUT /api/orders/:id/status
 * @desc    Update order status with validation and audit logging
 * @access  Admin, Manager (employees can only view)
 */
router.put('/:id/status',
  authorize(['admin', 'manager']),
  validateIntegerIdParam,
  validateUpdateOrderStatus,
  updateOrderStatus
);

/**
 * @route   POST /api/orders/:id/fulfill
 * @desc    Process order fulfillment with inventory allocation and updates
 * @access  Admin, Manager
 * @rateLimit 30 requests per 15 minutes
 */
router.post('/:id/fulfill',
  fulfillmentLimit,
  authorize(['admin', 'manager']),
  validateIntegerIdParam,
  validateFulfillOrder,
  fulfillOrder
);

module.exports = router;
