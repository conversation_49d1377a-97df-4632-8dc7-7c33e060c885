const express = require('express');
const rateLimit = require('express-rate-limit');
const containerController = require('../controllers/containerController');
const { authenticateToken, authorize } = require('../middleware/auth');
const {
  validateCreateContainer,
  validateUpdateContainer,
  validateGetContainersQuery,
  validateBulkImport,
  validateIntegerIdParam,
} = require('../middleware/validation');

const router = express.Router();

// Rate limiting for container creation
const containerCreationLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // limit each IP to 10 container creations per 15 minutes
  message: {
    error: 'Too many container creation attempts',
    message: 'Too many container creation attempts from this IP, please try again after 15 minutes.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiting for bulk import
const bulkImportLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // limit each IP to 5 bulk imports per hour
  message: {
    error: 'Too many bulk import attempts',
    message: 'Too many bulk import attempts from this IP, please try again after 1 hour.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * @route   POST /api/containers
 * @desc    Create a new container
 * @access  Private (Admin/Manager only)
 */
router.post(
  '/',
  containerCreationLimiter,
  authenticateToken,
  authorize(['admin', 'manager']),
  validateCreateContainer,
  containerController.createContainer
);

/**
 * @route   GET /api/containers
 * @desc    Get all containers with filtering and pagination
 * @access  Private (All authenticated users)
 */
router.get(
  '/',
  authenticateToken,
  validateGetContainersQuery,
  containerController.getAllContainers
);

/**
 * @route   GET /api/containers/:id
 * @desc    Get container by ID with detailed information
 * @access  Private (All authenticated users)
 */
router.get(
  '/:id',
  authenticateToken,
  validateIntegerIdParam,
  containerController.getContainerById
);

/**
 * @route   PUT /api/containers/:id
 * @desc    Update container with status workflow validation
 * @access  Private (Admin/Manager only)
 */
router.put(
  '/:id',
  authenticateToken,
  authorize(['admin', 'manager']),
  validateIntegerIdParam,
  validateUpdateContainer,
  containerController.updateContainer
);

/**
 * @route   DELETE /api/containers/:id
 * @desc    Soft delete container
 * @access  Private (Admin/Manager only)
 */
router.delete(
  '/:id',
  authenticateToken,
  authorize(['admin', 'manager']),
  validateIntegerIdParam,
  containerController.deleteContainer
);

/**
 * @route   POST /api/containers/:id/import
 * @desc    Bulk import inventory items to container
 * @access  Private (Admin/Manager only)
 */
router.post(
  '/:id/import',
  bulkImportLimiter,
  authenticateToken,
  authorize(['admin', 'manager']),
  validateIntegerIdParam,
  validateBulkImport,
  containerController.bulkImportInventory
);

/**
 * @route   GET /api/containers/:id/summary
 * @desc    Get container summary report
 * @access  Private (All authenticated users)
 */
router.get(
  '/:id/summary',
  authenticateToken,
  validateIntegerIdParam,
  containerController.getContainerSummary
);

// Health check for container service
router.get('/health', (req, res) => {
  res.json({
    service: 'Container Management Service',
    status: 'healthy',
    timestamp: new Date().toISOString(),
  });
});

module.exports = router;
