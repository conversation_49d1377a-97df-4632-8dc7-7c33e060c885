const express = require('express');
const rateLimit = require('express-rate-limit');
const { authenticateToken, authorize } = require('../middleware/auth');
const {
  validateCreateInventoryItem,
  validateUpdateInventoryItem,
  validateGetInventoryQuery,
  validateIntegerIdParam,
  validateBulkInventoryOperations,
  validateGenerateBarcode
} = require('../middleware/validation');
const {
  createInventoryItem,
  getInventoryItems,
  getInventoryItemById,
  updateInventoryItem,
  bulkInventoryOperations,
  getInventoryReports,
  generateItemBarcode
} = require('../controllers/inventoryController');

const router = express.Router();

// Rate limiting for inventory operations
const inventoryCreateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // Limit each IP to 50 requests per windowMs
  message: {
    error: 'Too many inventory creation requests',
    message: 'Please try again later',
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const inventoryBulkLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 bulk requests per windowMs
  message: {
    error: 'Too many bulk operation requests',
    message: 'Please try again later',
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const inventoryGeneralLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 200, // Limit each IP to 200 requests per windowMs
  message: {
    error: 'Too many inventory requests',
    message: 'Please try again later',
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Apply general rate limiting to all inventory routes
router.use(inventoryGeneralLimit);

// All routes require authentication
router.use(authenticateToken);

/**
 * @route   POST /api/inventory
 * @desc    Add individual laptop unit
 * @access  Admin, Manager, Employee
 * @rateLimit 50 requests per 15 minutes
 */
router.post('/',
  inventoryCreateLimit,
  authorize(['admin', 'manager', 'employee']),
  validateCreateInventoryItem,
  createInventoryItem
);

/**
 * @route   GET /api/inventory
 * @desc    List inventory with advanced filtering
 * @access  Admin, Manager, Employee
 * @query   page, limit, status, condition, warehouse, location, serial_number, product_id, container_id, date_from, date_to
 */
router.get('/',
  authorize(['admin', 'manager', 'employee']),
  validateGetInventoryQuery,
  getInventoryItems
);

/**
 * @route   GET /api/inventory/reports
 * @desc    Get inventory reports (aging, low stock alerts)
 * @access  Admin, Manager
 * @query   report_type (summary, aging, low_stock), thresholds
 */
router.get('/reports',
  authorize(['admin', 'manager']),
  getInventoryReports
);

/**
 * @route   POST /api/inventory/bulk
 * @desc    Bulk inventory operations
 * @access  Admin, Manager
 * @rateLimit 10 requests per 15 minutes
 * @body    operation (status_update, location_update, import_from_container), items, change_reason
 */
router.post('/bulk',
  inventoryBulkLimit,
  authorize(['admin', 'manager']),
  // Custom validation for bulk operations
  (req, res, next) => {
    const { operation, items } = req.body;

    if (!operation || !['status_update', 'location_update', 'import_from_container'].includes(operation)) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Operation must be one of: status_update, location_update, import_from_container',
        timestamp: new Date().toISOString(),
      });
    }

    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Items array is required and must not be empty',
        timestamp: new Date().toISOString(),
      });
    }

    if (items.length > 100) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Cannot process more than 100 items at once',
        timestamp: new Date().toISOString(),
      });
    }

    // Validate items based on operation type
    for (let i = 0; i < items.length; i++) {
      const item = items[i];

      if (operation === 'status_update') {
        if (!item.id || typeof item.id !== 'number' || item.id <= 0) {
          return res.status(400).json({
            error: 'Validation error',
            message: `Item ${i}: id is required and must be a positive number`,
            timestamp: new Date().toISOString(),
          });
        }
        if (!item.status || !['available', 'reserved', 'sold', 'returned'].includes(item.status)) {
          return res.status(400).json({
            error: 'Validation error',
            message: `Item ${i}: status must be one of: available, reserved, sold, returned`,
            timestamp: new Date().toISOString(),
          });
        }
      } else if (operation === 'location_update') {
        if (!item.id || typeof item.id !== 'number' || item.id <= 0) {
          return res.status(400).json({
            error: 'Validation error',
            message: `Item ${i}: id is required and must be a positive number`,
            timestamp: new Date().toISOString(),
          });
        }
      } else if (operation === 'import_from_container') {
        if (!item.product_id || typeof item.product_id !== 'number' || item.product_id <= 0) {
          return res.status(400).json({
            error: 'Validation error',
            message: `Item ${i}: product_id is required and must be a positive number`,
            timestamp: new Date().toISOString(),
          });
        }
        if (!item.container_id || typeof item.container_id !== 'number' || item.container_id <= 0) {
          return res.status(400).json({
            error: 'Validation error',
            message: `Item ${i}: container_id is required and must be a positive number`,
            timestamp: new Date().toISOString(),
          });
        }
        if (!item.serial_number || typeof item.serial_number !== 'string' || item.serial_number.length === 0) {
          return res.status(400).json({
            error: 'Validation error',
            message: `Item ${i}: serial_number is required and must be a non-empty string`,
            timestamp: new Date().toISOString(),
          });
        }
        if (!item.cost_price || typeof item.cost_price !== 'number' || item.cost_price <= 0) {
          return res.status(400).json({
            error: 'Validation error',
            message: `Item ${i}: cost_price is required and must be a positive number`,
            timestamp: new Date().toISOString(),
          });
        }
      }
    }

    next();
  },
  bulkInventoryOperations
);

/**
 * @route   GET /api/inventory/:id
 * @desc    Get specific item details with full history
 * @access  Admin, Manager, Employee
 * @param   id - Inventory item ID
 */
router.get('/:id',
  authorize(['admin', 'manager', 'employee']),
  validateIntegerIdParam,
  getInventoryItemById
);

/**
 * @route   PUT /api/inventory/:id
 * @desc    Update item status/condition with audit trail
 * @access  Admin, Manager, Employee (limited)
 * @param   id - Inventory item ID
 * @body    condition, status, location, warehouse, notes, change_reason
 */
router.put('/:id',
  authorize(['admin', 'manager', 'employee']),
  validateIntegerIdParam,
  validateUpdateInventoryItem,
  updateInventoryItem
);

/**
 * @route   POST /api/inventory/:id/barcode
 * @desc    Generate barcode/QR code for individual item tracking
 * @access  Admin, Manager, Employee
 * @param   id - Inventory item ID
 * @body    type (barcode, qr)
 */
router.post('/:id/barcode',
  authorize(['admin', 'manager', 'employee']),
  validateIntegerIdParam,
  validateGenerateBarcode,
  generateItemBarcode
);

// Error handling middleware for inventory routes
router.use((error, req, res, next) => {
  console.error('Inventory route error:', error);
  
  // Handle specific inventory-related errors
  if (error.name === 'SequelizeUniqueConstraintError') {
    return res.status(409).json({
      error: 'Duplicate entry',
      message: 'Serial number must be unique',
      timestamp: new Date().toISOString(),
    });
  }

  if (error.name === 'SequelizeForeignKeyConstraintError') {
    return res.status(400).json({
      error: 'Invalid reference',
      message: 'Referenced product or container does not exist',
      timestamp: new Date().toISOString(),
    });
  }

  if (error.name === 'SequelizeValidationError') {
    const validationErrors = error.errors.map(err => ({
      field: err.path,
      message: err.message
    }));

    return res.status(400).json({
      error: 'Validation error',
      message: 'Invalid input data',
      details: validationErrors,
      timestamp: new Date().toISOString(),
    });
  }

  // Pass to global error handler
  next(error);
});

module.exports = router;
