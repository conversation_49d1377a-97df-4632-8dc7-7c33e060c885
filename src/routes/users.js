const express = require('express');
const userController = require('../controllers/userController');
const { authenticateToken, authorize } = require('../middleware/auth');
const {
  validateUpdateUser,
  validateChangePassword,
  validateGetUsersQuery,
  validateUuidParam,
} = require('../middleware/validation');

const router = express.Router();

/**
 * @route   GET /api/users
 * @desc    Get all users with pagination and filtering
 * @access  Private (Admin/Manager only)
 */
router.get(
  '/',
  authenticateToken,
  authorize('admin', 'manager'),
  validateGetUsersQuery,
  userController.getAllUsers
);

/**
 * @route   GET /api/users/stats
 * @desc    Get user statistics
 * @access  Private (Admin/Manager only)
 */
router.get(
  '/stats',
  authenticateToken,
  authorize('admin', 'manager'),
  userController.getUserStats
);

/**
 * @route   GET /api/users/:id
 * @desc    Get user by ID
 * @access  Private (Own profile or Admin/Manager)
 */
router.get(
  '/:id',
  authenticateToken,
  validateUuidParam,
  userController.getUserById
);

/**
 * @route   PUT /api/users/:id
 * @desc    Update user
 * @access  Private (Own profile or Admin/Manager with restrictions)
 */
router.put(
  '/:id',
  authenticateToken,
  validateUuidParam,
  validateUpdateUser,
  userController.updateUser
);

/**
 * @route   DELETE /api/users/:id
 * @desc    Deactivate user
 * @access  Private (Admin only)
 */
router.delete(
  '/:id',
  authenticateToken,
  authorize('admin'),
  validateUuidParam,
  userController.deactivateUser
);

/**
 * @route   PUT /api/users/:id/password
 * @desc    Change user password
 * @access  Private (Own password only)
 */
router.put(
  '/:id/password',
  authenticateToken,
  validateUuidParam,
  validateChangePassword,
  userController.changePassword
);

// Health check for user service
router.get('/health', (req, res) => {
  res.json({
    service: 'User Management Service',
    status: 'healthy',
    timestamp: new Date().toISOString(),
  });
});

module.exports = router;
