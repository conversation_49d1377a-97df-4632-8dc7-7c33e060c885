const express = require('express');
const rateLimit = require('express-rate-limit');
const productController = require('../controllers/productController');
const { authenticateToken, authorize } = require('../middleware/auth');
const {
  validateCreateProduct,
  validateUpdateProduct,
  validateGetProductsQuery,
  validateProductBulkImport,
  validateIntegerIdParam,
} = require('../middleware/validation');

const router = express.Router();

// Rate limiting for product creation
const productCreationLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 20, // limit each IP to 20 product creations per 15 minutes
  message: {
    error: 'Too many product creation attempts',
    message: 'Too many product creation attempts from this IP, please try again after 15 minutes.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiting for bulk import
const bulkImportLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // limit each IP to 3 bulk imports per hour
  message: {
    error: 'Too many bulk import attempts',
    message: 'Too many bulk import attempts from this IP, please try again after 1 hour.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiting for analytics endpoint
const analyticsLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 10, // limit each IP to 10 analytics requests per 5 minutes
  message: {
    error: 'Too many analytics requests',
    message: 'Too many analytics requests from this IP, please try again after 5 minutes.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * @route   GET /api/products/analytics
 * @desc    Get product analytics and performance metrics
 * @access  Private (Admin/Manager only)
 */
router.get(
  '/analytics',
  analyticsLimiter,
  authenticateToken,
  authorize(['admin', 'manager']),
  productController.getProductAnalytics
);

/**
 * @route   POST /api/products/bulk-import
 * @desc    Bulk import products from array
 * @access  Private (Admin/Manager only)
 */
router.post(
  '/bulk-import',
  bulkImportLimiter,
  authenticateToken,
  authorize(['admin', 'manager']),
  validateProductBulkImport,
  productController.bulkImportProducts
);

/**
 * @route   POST /api/products
 * @desc    Create a new product
 * @access  Private (Admin/Manager only)
 */
router.post(
  '/',
  productCreationLimiter,
  authenticateToken,
  authorize(['admin', 'manager']),
  validateCreateProduct,
  productController.createProduct
);

/**
 * @route   GET /api/products
 * @desc    Get all products with filtering and search
 * @access  Private (All authenticated users)
 */
router.get(
  '/',
  authenticateToken,
  validateGetProductsQuery,
  productController.getProducts
);

/**
 * @route   GET /api/products/:id
 * @desc    Get a single product by ID
 * @access  Private (All authenticated users)
 */
router.get(
  '/:id',
  authenticateToken,
  validateIntegerIdParam,
  productController.getProductById
);

/**
 * @route   PUT /api/products/:id
 * @desc    Update a product
 * @access  Private (Admin/Manager only)
 */
router.put(
  '/:id',
  authenticateToken,
  authorize(['admin', 'manager']),
  validateIntegerIdParam,
  validateUpdateProduct,
  productController.updateProduct
);

/**
 * @route   DELETE /api/products/:id
 * @desc    Soft delete a product
 * @access  Private (Admin only)
 */
router.delete(
  '/:id',
  authenticateToken,
  authorize(['admin']),
  validateIntegerIdParam,
  productController.deleteProduct
);

// Health check for products service
router.get('/health', (req, res) => {
  res.json({
    service: 'Products Service',
    status: 'healthy',
    timestamp: new Date().toISOString(),
  });
});

module.exports = router;
