const express = require('express');
const rateLimit = require('express-rate-limit');
const { authenticateToken, authorize } = require('../middleware/auth');
const {
  validateCreateCustomer,
  validateUpdateCustomer,
  validateGetCustomersQuery,
  validateIntegerIdParam,
  validateCreateCustomerCommunication,
  validateCustomerBulkImport
} = require('../middleware/validation');
const {
  createCustomer,
  getCustomers,
  getCustomerById,
  updateCustomer,
  getCustomerOrders,
  createCustomerCommunication,
  bulkImportCustomers
} = require('../controllers/customerController');

const router = express.Router();

// Rate limiting for customer creation
const customerCreateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 customer creations per windowMs
  message: {
    error: 'Too many customer creation requests',
    message: 'Please try again later',
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiting for bulk operations
const customerBulkLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // Limit each IP to 5 bulk operations per hour
  message: {
    error: 'Too many bulk operation requests',
    message: 'Please try again later',
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// General rate limiting for customer routes
const customerGeneralLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 200, // Limit each IP to 200 requests per windowMs
  message: {
    error: 'Too many customer requests',
    message: 'Please try again later',
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Apply general rate limiting to all customer routes
router.use(customerGeneralLimit);

// All routes require authentication
router.use(authenticateToken);

/**
 * @route   POST /api/customers
 * @desc    Create new retailer customer
 * @access  Admin, Manager
 * @rateLimit 10 requests per 15 minutes
 */
router.post('/',
  customerCreateLimit,
  authorize(['admin', 'manager']),
  validateCreateCustomer,
  createCustomer
);

/**
 * @route   GET /api/customers
 * @desc    List customers with filtering and search
 * @access  Admin, Manager, Employee
 * @query   page, limit, search, pricing_tier, status, credit_limit_min, credit_limit_max
 */
router.get('/',
  authorize(['admin', 'manager', 'employee']),
  validateGetCustomersQuery,
  getCustomers
);

/**
 * @route   POST /api/customers/bulk-import
 * @desc    Bulk customer import from CSV
 * @access  Admin, Manager
 * @rateLimit 5 requests per hour
 */
router.post('/bulk-import',
  customerBulkLimit,
  authorize(['admin', 'manager']),
  validateCustomerBulkImport,
  bulkImportCustomers
);

/**
 * @route   GET /api/customers/:id
 * @desc    Get detailed customer profile
 * @access  Admin, Manager, Employee
 * @param   id - Customer ID
 */
router.get('/:id',
  authorize(['admin', 'manager', 'employee']),
  validateIntegerIdParam,
  getCustomerById
);

/**
 * @route   PUT /api/customers/:id
 * @desc    Update customer information
 * @access  Admin, Manager (credit limit changes admin/manager only)
 * @param   id - Customer ID
 * @body    company_name, contact_person, email, phone, address, credit_limit, pricing_tier, status, payment_terms
 */
router.put('/:id',
  authorize(['admin', 'manager', 'employee']),
  validateIntegerIdParam,
  validateUpdateCustomer,
  updateCustomer
);

/**
 * @route   GET /api/customers/:id/orders
 * @desc    Get customer order history
 * @access  Admin, Manager, Employee
 * @param   id - Customer ID
 * @query   page, limit, status, date_from, date_to
 */
router.get('/:id/orders',
  authorize(['admin', 'manager', 'employee']),
  validateIntegerIdParam,
  // Custom validation for order query parameters
  (req, res, next) => {
    const { page = 1, limit = 20, status, date_from, date_to } = req.query;
    
    // Validate page and limit
    if (isNaN(page) || page < 1) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Page must be a positive integer',
        timestamp: new Date().toISOString(),
      });
    }
    
    if (isNaN(limit) || limit < 1 || limit > 100) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Limit must be between 1 and 100',
        timestamp: new Date().toISOString(),
      });
    }

    // Validate status if provided
    if (status && !['pending', 'confirmed', 'shipped', 'delivered', 'cancelled'].includes(status)) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Status must be one of: pending, confirmed, shipped, delivered, cancelled',
        timestamp: new Date().toISOString(),
      });
    }

    // Validate dates if provided
    if (date_from && isNaN(Date.parse(date_from))) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'date_from must be a valid date',
        timestamp: new Date().toISOString(),
      });
    }

    if (date_to && isNaN(Date.parse(date_to))) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'date_to must be a valid date',
        timestamp: new Date().toISOString(),
      });
    }

    if (date_from && date_to && new Date(date_from) > new Date(date_to)) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'date_from must be before date_to',
        timestamp: new Date().toISOString(),
      });
    }

    next();
  },
  getCustomerOrders
);

/**
 * @route   POST /api/customers/:id/communications
 * @desc    Create customer communication record
 * @access  Admin, Manager, Employee
 * @param   id - Customer ID
 * @body    type, subject, content, direction, priority, follow_up_required, follow_up_date, communication_date
 */
router.post('/:id/communications',
  authorize(['admin', 'manager', 'employee']),
  validateIntegerIdParam,
  validateCreateCustomerCommunication,
  createCustomerCommunication
);

// Error handling middleware for customer routes
router.use((error, req, res, next) => {
  console.error('Customer route error:', error);
  
  // Handle specific customer-related errors
  if (error.name === 'SequelizeUniqueConstraintError') {
    return res.status(409).json({
      error: 'Duplicate entry',
      message: 'Email address must be unique',
      timestamp: new Date().toISOString(),
    });
  }

  if (error.name === 'SequelizeForeignKeyConstraintError') {
    return res.status(400).json({
      error: 'Invalid reference',
      message: 'Referenced customer or user does not exist',
      timestamp: new Date().toISOString(),
    });
  }

  if (error.name === 'SequelizeValidationError') {
    return res.status(400).json({
      error: 'Validation error',
      message: error.errors.map(e => e.message).join(', '),
      timestamp: new Date().toISOString(),
    });
  }

  // Default error response
  res.status(500).json({
    error: 'Internal server error',
    message: 'An unexpected error occurred',
    timestamp: new Date().toISOString(),
  });
});

module.exports = router;
