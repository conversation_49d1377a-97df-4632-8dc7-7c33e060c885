const express = require('express');
const rateLimit = require('express-rate-limit');
const { authenticateToken, authorize } = require('../middleware/auth');
const reportController = require('../controllers/reportController');
const { validateDateRange, validateExportFormat } = require('../middleware/validation');
const { reportCacheMiddleware } = require('../middleware/reportCache');

const router = express.Router();

// Rate limiting for report endpoints
const reportLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // Limit each IP to 10 report requests per minute
  message: {
    error: 'Too many report requests',
    message: 'Please try again later',
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Apply authentication and rate limiting to all report routes
router.use(authenticateToken);
router.use(reportLimiter);

// Inventory Reports - Admin and Manager access
router.get('/inventory',
  authorize(['admin', 'manager']),
  validateDateRange,
  validateExportFormat,
  reportCacheMiddleware.inventory,
  reportController.getInventoryReport
);

// Sales Reports - Admin and Manager access
router.get('/sales',
  authorize(['admin', 'manager']),
  validateDateRange,
  validateExportFormat,
  reportCacheMiddleware.sales,
  reportController.getSalesReport
);

// Container Reports - Admin and Manager access
router.get('/containers',
  authorize(['admin', 'manager']),
  validateDateRange,
  validateExportFormat,
  reportCacheMiddleware.containers,
  reportController.getContainerReport
);

// Customer Reports - Admin and Manager access
router.get('/customers',
  authorize(['admin', 'manager']),
  validateDateRange,
  validateExportFormat,
  reportCacheMiddleware.customers,
  reportController.getCustomerReport
);

// Dashboard - All authenticated users (role-based data filtering in controller)
router.get('/dashboard',
  validateDateRange,
  reportCacheMiddleware.dashboard,
  reportController.getDashboardReport
);

module.exports = router;
