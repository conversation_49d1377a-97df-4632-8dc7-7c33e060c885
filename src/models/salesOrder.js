'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class SalesOrder extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // SalesOrder belongs to Customer
      SalesOrder.belongsTo(models.Customer, {
        foreignKey: 'customer_id',
        as: 'customer',
        onDelete: 'CASCADE'
      });

      // SalesOrder has many OrderItems
      SalesOrder.hasMany(models.OrderItem, {
        foreignKey: 'order_id',
        as: 'orderItems',
        onDelete: 'CASCADE'
      });
    }

    /**
     * Validate status transition
     */
    canTransitionTo(newStatus) {
      const validTransitions = {
        'pending': ['confirmed', 'cancelled'],
        'confirmed': ['processing', 'cancelled'],
        'processing': ['shipped', 'cancelled'],
        'shipped': ['delivered'],
        'delivered': ['completed'],
        'completed': [],
        'cancelled': []
      };

      return validTransitions[this.status]?.includes(newStatus) || false;
    }

    /**
     * Check if order can be modified
     */
    canBeModified() {
      return ['pending', 'confirmed'].includes(this.status);
    }

    /**
     * Check if order can be cancelled
     */
    canBeCancelled() {
      return ['pending', 'confirmed', 'processing'].includes(this.status);
    }

    /**
     * Calculate order total from order items
     */
    async calculateTotal() {
      const orderItems = await this.getOrderItems();
      return orderItems.reduce((total, item) => {
        return total + (parseFloat(item.unit_price) * item.quantity);
      }, 0);
    }

    /**
     * Get order summary with customer and items
     */
    async getOrderSummary() {
      const customer = await this.getCustomer();
      const orderItems = await this.getOrderItems({
        include: [{
          model: this.sequelize.models.InventoryItem,
          as: 'inventoryItem',
          include: [{
            model: this.sequelize.models.Product,
            as: 'product'
          }]
        }]
      });

      return {
        order: this.toJSON(),
        customer: customer ? customer.toJSON() : null,
        items: orderItems.map(item => item.toJSON()),
        itemCount: orderItems.length,
        totalQuantity: orderItems.reduce((sum, item) => sum + item.quantity, 0)
      };
    }
  }

  SalesOrder.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    customer_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'customers',
        key: 'id'
      },
      validate: {
        notNull: {
          msg: 'Customer ID is required'
        },
        isInt: {
          msg: 'Customer ID must be an integer'
        }
      }
    },
    order_date: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      validate: {
        isDate: {
          msg: 'Order date must be a valid date'
        }
      }
    },
    status: {
      type: DataTypes.ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'completed', 'cancelled'),
      allowNull: false,
      defaultValue: 'pending',
      validate: {
        isIn: {
          args: [['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'completed', 'cancelled']],
          msg: 'Status must be one of: pending, confirmed, processing, shipped, delivered, completed, cancelled'
        }
      }
    },
    total_amount: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.00,
      validate: {
        isDecimal: {
          msg: 'Total amount must be a valid decimal number'
        },
        min: {
          args: [0],
          msg: 'Total amount must be non-negative'
        }
      }
    }
  }, {
    sequelize,
    modelName: 'SalesOrder',
    tableName: 'sales_orders',
    timestamps: true,
    underscored: true,
    paranoid: true, // Enable soft delete
    indexes: [
      {
        fields: ['customer_id']
      },
      {
        fields: ['order_date']
      },
      {
        fields: ['status']
      },
      {
        fields: ['total_amount']
      },
      {
        fields: ['deleted_at']
      }
    ]
  });

  return SalesOrder;
};
