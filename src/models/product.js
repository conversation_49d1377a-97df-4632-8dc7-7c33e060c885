'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Product extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // Product has many InventoryItems
      Product.hasMany(models.InventoryItem, {
        foreignKey: 'product_id',
        as: 'inventoryItems',
        onDelete: 'RESTRICT'
      });

      // Product belongs to User (created by)
      Product.belongsTo(models.User, {
        foreignKey: 'created_by',
        as: 'creator',
        onDelete: 'SET NULL'
      });

      // Product belongs to User (updated by)
      Product.belongsTo(models.User, {
        foreignKey: 'updated_by',
        as: 'updater',
        onDelete: 'SET NULL'
      });
    }

    /**
     * Instance method to get product with inventory summary
     */
    async getInventorySummary() {
      const inventoryItems = await this.getInventoryItems();
      const totalItems = inventoryItems.length;
      const availableItems = inventoryItems.filter(item => item.status === 'available').length;
      const soldItems = inventoryItems.filter(item => item.status === 'sold').length;
      const totalValue = inventoryItems.reduce((sum, item) => sum + parseFloat(item.cost_price || 0), 0);

      return {
        total_items: totalItems,
        available_items: availableItems,
        sold_items: soldItems,
        total_value: totalValue.toFixed(2),
        average_cost: totalItems > 0 ? (totalValue / totalItems).toFixed(2) : '0.00'
      };
    }

    /**
     * Instance method to add price history entry
     */
    async addPriceHistory(newPrice, userId, reason = 'Price update') {
      const currentHistory = this.price_history || [];
      const priceEntry = {
        price: parseFloat(newPrice),
        date: new Date().toISOString(),
        updated_by: userId,
        reason: reason
      };

      currentHistory.push(priceEntry);
      this.price_history = currentHistory;
      await this.save();
    }

    /**
     * Instance method to get search text for full-text search
     */
    getSearchText() {
      const specs = this.specifications_json || {};
      const specText = Object.values(specs).join(' ');
      return `${this.brand} ${this.model} ${specText}`.toLowerCase();
    }
  }

  Product.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    brand: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'Brand is required'
        },
        len: {
          args: [1, 100],
          msg: 'Brand must be between 1 and 100 characters'
        }
      }
    },
    model: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'Model is required'
        },
        len: {
          args: [1, 100],
          msg: 'Model must be between 1 and 100 characters'
        }
      }
    },
    specifications_json: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {},
      validate: {
        isValidJSON(value) {
          if (value && typeof value !== 'object') {
            throw new Error('Specifications must be a valid JSON object');
          }
          // Validate common laptop specification fields
          if (value) {
            const allowedFields = [
              'cpu', 'ram', 'storage', 'screen_size', 'resolution',
              'graphics', 'operating_system', 'weight', 'color',
              'battery_life', 'ports', 'wireless', 'warranty'
            ];
            const invalidFields = Object.keys(value).filter(
              key => !allowedFields.includes(key) && !key.startsWith('custom_')
            );
            if (invalidFields.length > 0) {
              throw new Error(`Invalid specification fields: ${invalidFields.join(', ')}`);
            }
          }
        }
      }
    },
    base_price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        isDecimal: {
          msg: 'Base price must be a valid decimal number'
        },
        min: {
          args: [0.01],
          msg: 'Base price must be positive'
        }
      }
    },
    image_url: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isUrl: {
          msg: 'Image URL must be a valid URL'
        },
        isValidImageUrl(value) {
          if (value) {
            const validExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];
            const hasValidExtension = validExtensions.some(ext =>
              value.toLowerCase().includes(ext)
            );
            if (!hasValidExtension) {
              throw new Error('Image URL must point to a valid image file (jpg, jpeg, png, webp, gif)');
            }
          }
        }
      }
    },
    price_history: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: [],
      validate: {
        isValidPriceHistory(value) {
          if (value && Array.isArray(value)) {
            for (const entry of value) {
              if (!entry.price || !entry.date || typeof entry.price !== 'number') {
                throw new Error('Price history entries must have price and date fields');
              }
            }
          }
        }
      }
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    updated_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    deleted_at: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'Product',
    tableName: 'products',
    timestamps: true,
    underscored: true,
    paranoid: true,
    deletedAt: 'deleted_at',
    indexes: [
      {
        fields: ['brand']
      },
      {
        fields: ['model']
      },
      {
        fields: ['brand', 'model']
      },
      {
        fields: ['base_price']
      },
      {
        fields: ['created_by']
      },
      {
        fields: ['updated_by']
      },
      {
        fields: ['deleted_at']
      },
      {
        name: 'products_search_idx',
        fields: ['brand', 'model'],
        using: 'gin',
        operator: 'gin_trgm_ops'
      },
      {
        name: 'products_specifications_idx',
        fields: ['specifications_json'],
        using: 'gin'
      }
    ],
    hooks: {
      beforeCreate: (product, options) => {
        if (options.user) {
          product.created_by = options.user.id;
          product.updated_by = options.user.id;
        }
      },
      beforeUpdate: (product, options) => {
        if (options.user) {
          product.updated_by = options.user.id;
        }
      }
    }
  });

  return Product;
};
