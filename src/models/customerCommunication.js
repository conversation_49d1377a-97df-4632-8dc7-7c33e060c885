'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class CustomerCommunication extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // CustomerCommunication belongs to Customer
      CustomerCommunication.belongsTo(models.Customer, {
        foreignKey: 'customer_id',
        as: 'customer',
        onDelete: 'CASCADE'
      });

      // CustomerCommunication belongs to User (who created the communication)
      CustomerCommunication.belongsTo(models.User, {
        foreignKey: 'created_by',
        as: 'creator',
        onDelete: 'SET NULL'
      });
    }

    /**
     * Get formatted communication type
     */
    getFormattedType() {
      const types = {
        'email': 'Email',
        'phone': 'Phone Call',
        'meeting': 'Meeting',
        'note': 'Internal Note',
        'follow_up': 'Follow-up',
        'complaint': 'Complaint',
        'support': 'Support Request'
      };
      return types[this.type] || this.type;
    }

    /**
     * Check if communication requires follow-up
     */
    requiresFollowUp() {
      return this.follow_up_required && (!this.follow_up_date || new Date(this.follow_up_date) <= new Date());
    }
  }

  CustomerCommunication.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    customer_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'customers',
        key: 'id'
      },
      validate: {
        notNull: {
          msg: 'Customer ID is required'
        },
        isInt: {
          msg: 'Customer ID must be an integer'
        }
      }
    },
    type: {
      type: DataTypes.ENUM('email', 'phone', 'meeting', 'note', 'follow_up', 'complaint', 'support'),
      allowNull: false,
      validate: {
        isIn: {
          args: [['email', 'phone', 'meeting', 'note', 'follow_up', 'complaint', 'support']],
          msg: 'Type must be one of: email, phone, meeting, note, follow_up, complaint, support'
        }
      }
    },
    subject: {
      type: DataTypes.STRING(200),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'Subject is required'
        },
        len: {
          args: [1, 200],
          msg: 'Subject must be between 1 and 200 characters'
        }
      }
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'Content is required'
        },
        len: {
          args: [1, 10000],
          msg: 'Content must be between 1 and 10000 characters'
        }
      }
    },
    direction: {
      type: DataTypes.ENUM('inbound', 'outbound'),
      allowNull: false,
      defaultValue: 'outbound',
      validate: {
        isIn: {
          args: [['inbound', 'outbound']],
          msg: 'Direction must be either inbound or outbound'
        }
      }
    },
    priority: {
      type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
      allowNull: false,
      defaultValue: 'medium',
      validate: {
        isIn: {
          args: [['low', 'medium', 'high', 'urgent']],
          msg: 'Priority must be one of: low, medium, high, urgent'
        }
      }
    },
    status: {
      type: DataTypes.ENUM('open', 'in_progress', 'resolved', 'closed'),
      allowNull: false,
      defaultValue: 'open',
      validate: {
        isIn: {
          args: [['open', 'in_progress', 'resolved', 'closed']],
          msg: 'Status must be one of: open, in_progress, resolved, closed'
        }
      }
    },
    follow_up_required: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    follow_up_date: {
      type: DataTypes.DATE,
      allowNull: true,
      validate: {
        isDate: {
          msg: 'Follow-up date must be a valid date'
        },
        isAfterToday(value) {
          if (value && new Date(value) <= new Date()) {
            throw new Error('Follow-up date must be in the future');
          }
        }
      }
    },
    communication_date: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      validate: {
        isDate: {
          msg: 'Communication date must be a valid date'
        }
      }
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      validate: {
        notNull: {
          msg: 'Created by user ID is required'
        },
        isUUID: {
          args: 4,
          msg: 'Created by must be a valid UUID'
        }
      }
    },
    metadata: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {},
      validate: {
        isValidMetadata(value) {
          if (value && typeof value !== 'object') {
            throw new Error('Metadata must be a valid JSON object');
          }
        }
      }
    }
  }, {
    sequelize,
    modelName: 'CustomerCommunication',
    tableName: 'customer_communications',
    timestamps: true,
    underscored: true,
    paranoid: true, // Soft delete
    indexes: [
      {
        fields: ['customer_id']
      },
      {
        fields: ['type']
      },
      {
        fields: ['status']
      },
      {
        fields: ['priority']
      },
      {
        fields: ['communication_date']
      },
      {
        fields: ['follow_up_date']
      },
      {
        fields: ['created_by']
      },
      {
        fields: ['customer_id', 'communication_date']
      },
      {
        fields: ['follow_up_required', 'follow_up_date']
      }
    ]
  });

  return CustomerCommunication;
};
