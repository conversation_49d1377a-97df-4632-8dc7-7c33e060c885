'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Customer extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // Customer has many SalesOrders
      Customer.hasMany(models.SalesOrder, {
        foreignKey: 'customer_id',
        as: 'salesOrders',
        onDelete: 'CASCADE'
      });

      // Customer has many CustomerCommunications
      Customer.hasMany(models.CustomerCommunication, {
        foreignKey: 'customer_id',
        as: 'communications',
        onDelete: 'CASCADE'
      });
    }

    /**
     * Calculate credit utilization percentage
     */
    getCreditUtilization() {
      if (this.credit_limit === 0) return 0;
      return (this.credit_used / this.credit_limit) * 100;
    }

    /**
     * Check if customer has available credit
     */
    hasAvailableCredit(amount = 0) {
      return (this.credit_used + amount) <= this.credit_limit;
    }

    /**
     * Get available credit amount
     */
    getAvailableCredit() {
      return Math.max(0, this.credit_limit - this.credit_used);
    }

    /**
     * Check if customer is active and can place orders
     */
    canPlaceOrders() {
      return this.status === 'active' && this.hasAvailableCredit();
    }

    /**
     * Get pricing tier discount percentage
     */
    getPricingDiscount() {
      const discounts = {
        'bronze': 0,
        'silver': 5,
        'gold': 10,
        'platinum': 15
      };
      return discounts[this.pricing_tier] || 0;
    }
  }

  Customer.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    company_name: {
      type: DataTypes.STRING(200),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'Company name is required'
        },
        len: {
          args: [1, 200],
          msg: 'Company name must be between 1 and 200 characters'
        }
      }
    },
    contact_person: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'Contact person is required'
        },
        len: {
          args: [1, 100],
          msg: 'Contact person must be between 1 and 100 characters'
        }
      }
    },
    email: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true,
      validate: {
        isEmail: {
          msg: 'Must be a valid email address'
        },
        notEmpty: {
          msg: 'Email is required'
        }
      }
    },
    phone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      validate: {
        is: {
          args: /^[\+]?[1-9][\d]{0,15}$/,
          msg: 'Phone number must be valid (E.164 format preferred)'
        }
      }
    },
    address: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {},
      validate: {
        isValidAddress(value) {
          if (value && typeof value !== 'object') {
            throw new Error('Address must be a valid JSON object');
          }
        }
      }
    },
    credit_limit: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 5000.00,
      validate: {
        isDecimal: {
          msg: 'Credit limit must be a valid decimal number'
        },
        min: {
          args: [0],
          msg: 'Credit limit must be non-negative'
        }
      }
    },
    credit_used: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.00,
      validate: {
        isDecimal: {
          msg: 'Credit used must be a valid decimal number'
        },
        min: {
          args: [0],
          msg: 'Credit used must be non-negative'
        }
      }
    },
    pricing_tier: {
      type: DataTypes.ENUM('bronze', 'silver', 'gold', 'platinum'),
      allowNull: false,
      defaultValue: 'bronze',
      validate: {
        isIn: {
          args: [['bronze', 'silver', 'gold', 'platinum']],
          msg: 'Pricing tier must be one of: bronze, silver, gold, platinum'
        }
      }
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive', 'suspended'),
      allowNull: false,
      defaultValue: 'active',
      validate: {
        isIn: {
          args: [['active', 'inactive', 'suspended']],
          msg: 'Status must be one of: active, inactive, suspended'
        }
      }
    },
    payment_terms: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 30,
      validate: {
        isInt: {
          msg: 'Payment terms must be an integer (days)'
        },
        min: {
          args: [0],
          msg: 'Payment terms must be non-negative'
        },
        max: {
          args: [365],
          msg: 'Payment terms cannot exceed 365 days'
        }
      }
    }
  }, {
    sequelize,
    modelName: 'Customer',
    tableName: 'customers',
    timestamps: true,
    underscored: true,
    paranoid: true, // Soft delete
    indexes: [
      {
        fields: ['company_name']
      },
      {
        fields: ['email'],
        unique: true
      },
      {
        fields: ['pricing_tier']
      },
      {
        fields: ['status']
      },
      {
        fields: ['credit_limit']
      },
      {
        fields: ['contact_person']
      },
      {
        name: 'customers_search_idx',
        fields: ['company_name', 'contact_person'],
        using: 'gin',
        operator: 'gin_trgm_ops'
      }
    ]
  });

  return Customer;
};
