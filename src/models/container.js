'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Container extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // Container has many InventoryItems
      Container.hasMany(models.InventoryItem, {
        foreignKey: 'container_id',
        as: 'inventoryItems',
        onDelete: 'CASCADE'
      });
    }
  }

  Container.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    container_number: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: {
        name: 'container_number_unique',
        msg: 'Container number must be unique'
      },
      validate: {
        notEmpty: {
          msg: 'Container number is required'
        },
        len: {
          args: [1, 50],
          msg: 'Container number must be between 1 and 50 characters'
        }
      }
    },
    arrival_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      validate: {
        notNull: {
          msg: 'Arrival date is required'
        },
        isDate: {
          msg: 'Arrival date must be a valid date'
        }
      }
    },
    status: {
      type: DataTypes.ENUM('arrived', 'processing', 'completed'),
      allowNull: false,
      defaultValue: 'arrived',
      validate: {
        isIn: {
          args: [['arrived', 'processing', 'completed']],
          msg: 'Status must be one of: arrived, processing, completed'
        }
      }
    },
    total_items: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      validate: {
        isInt: {
          msg: 'Total items must be an integer'
        },
        min: {
          args: [0],
          msg: 'Total items must be non-negative'
        }
      }
    },
    deleted_at: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: null
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    updated_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    sequelize,
    modelName: 'Container',
    tableName: 'containers',
    timestamps: true,
    underscored: true,
    paranoid: true,
    deletedAt: 'deleted_at',
    indexes: [
      {
        unique: true,
        fields: ['container_number'],
        where: {
          deleted_at: null
        }
      },
      {
        fields: ['status']
      },
      {
        fields: ['arrival_date']
      },
      {
        fields: ['deleted_at']
      }
    ]
  });

  return Container;
};
