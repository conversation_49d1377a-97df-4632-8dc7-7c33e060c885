'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class OrderItem extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // OrderItem belongs to SalesOrder
      OrderItem.belongsTo(models.SalesOrder, {
        foreignKey: 'order_id',
        as: 'salesOrder',
        onDelete: 'CASCADE'
      });

      // OrderItem belongs to InventoryItem
      OrderItem.belongsTo(models.InventoryItem, {
        foreignKey: 'inventory_item_id',
        as: 'inventoryItem',
        onDelete: 'RESTRICT'
      });
    }
  }

  OrderItem.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    order_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'sales_orders',
        key: 'id'
      },
      validate: {
        notNull: {
          msg: 'Order ID is required'
        },
        isInt: {
          msg: 'Order ID must be an integer'
        }
      }
    },
    inventory_item_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'inventory_items',
        key: 'id'
      },
      validate: {
        notNull: {
          msg: 'Inventory item ID is required'
        },
        isInt: {
          msg: 'Inventory item ID must be an integer'
        }
      }
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      validate: {
        isInt: {
          msg: 'Quantity must be an integer'
        },
        min: {
          args: [1],
          msg: 'Quantity must be positive'
        }
      }
    },
    unit_price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        isDecimal: {
          msg: 'Unit price must be a valid decimal number'
        },
        min: {
          args: [0.01],
          msg: 'Unit price must be positive'
        }
      }
    }
  }, {
    sequelize,
    modelName: 'OrderItem',
    tableName: 'order_items',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['order_id']
      },
      {
        fields: ['inventory_item_id']
      },
      {
        unique: true,
        fields: ['inventory_item_id'],
        name: 'unique_inventory_item_per_order'
      }
    ]
  });

  return OrderItem;
};
