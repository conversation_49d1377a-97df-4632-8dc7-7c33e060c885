'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class InventoryItem extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // InventoryItem belongs to Product
      InventoryItem.belongsTo(models.Product, {
        foreignKey: 'product_id',
        as: 'product',
        onDelete: 'RESTRICT'
      });

      // InventoryItem belongs to Container
      InventoryItem.belongsTo(models.Container, {
        foreignKey: 'container_id',
        as: 'container',
        onDelete: 'CASCADE'
      });

      // InventoryItem has one OrderItem
      InventoryItem.hasOne(models.OrderItem, {
        foreignKey: 'inventory_item_id',
        as: 'orderItem',
        onDelete: 'RESTRICT'
      });

      // InventoryItem has many InventoryAudit records
      InventoryItem.hasMany(models.InventoryAudit, {
        foreignKey: 'inventory_item_id',
        as: 'auditHistory',
        onDelete: 'CASCADE'
      });
    }

    /**
     * Validate status transition
     */
    canTransitionTo(newStatus) {
      const validTransitions = {
        'available': ['reserved', 'sold'],
        'reserved': ['available', 'sold'],
        'sold': ['returned'],
        'returned': ['available', 'reserved']
      };

      return validTransitions[this.status]?.includes(newStatus) || false;
    }

    /**
     * Get status workflow history
     */
    async getStatusHistory() {
      const InventoryAudit = this.sequelize.models.InventoryAudit;
      return await InventoryAudit.findAll({
        where: {
          inventory_item_id: this.id,
          field_name: 'status'
        },
        order: [['created_at', 'DESC']]
      });
    }

    /**
     * Get condition change history
     */
    async getConditionHistory() {
      const InventoryAudit = this.sequelize.models.InventoryAudit;
      return await InventoryAudit.findAll({
        where: {
          inventory_item_id: this.id,
          field_name: 'condition'
        },
        order: [['created_at', 'DESC']]
      });
    }

    /**
     * Calculate days in current status
     */
    getDaysInStatus() {
      const now = new Date();
      const updatedAt = new Date(this.updated_at);
      const diffTime = Math.abs(now - updatedAt);
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }

    /**
     * Check if item is available for sale
     */
    isAvailable() {
      return this.status === 'available' &&
             ['new', 'refurbished'].includes(this.condition) &&
             !this.deleted_at;
    }

    /**
     * Generate barcode data
     */
    getBarcodeData() {
      return {
        serial_number: this.serial_number,
        product_id: this.product_id,
        id: this.id
      };
    }
  }

  InventoryItem.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    product_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'products',
        key: 'id'
      },
      validate: {
        notNull: {
          msg: 'Product ID is required'
        },
        isInt: {
          msg: 'Product ID must be an integer'
        }
      }
    },
    container_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'containers',
        key: 'id'
      },
      validate: {
        notNull: {
          msg: 'Container ID is required'
        },
        isInt: {
          msg: 'Container ID must be an integer'
        }
      }
    },
    serial_number: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: {
        name: 'serial_number_unique',
        msg: 'Serial number must be unique'
      },
      validate: {
        notEmpty: {
          msg: 'Serial number is required'
        },
        len: {
          args: [1, 100],
          msg: 'Serial number must be between 1 and 100 characters'
        }
      }
    },
    condition: {
      type: DataTypes.ENUM('new', 'refurbished', 'damaged', 'defective'),
      allowNull: false,
      defaultValue: 'new',
      validate: {
        isIn: {
          args: [['new', 'refurbished', 'damaged', 'defective']],
          msg: 'Condition must be one of: new, refurbished, damaged, defective'
        }
      }
    },
    status: {
      type: DataTypes.ENUM('available', 'reserved', 'sold', 'returned'),
      allowNull: false,
      defaultValue: 'available',
      validate: {
        isIn: {
          args: [['available', 'reserved', 'sold', 'returned']],
          msg: 'Status must be one of: available, reserved, sold, returned'
        }
      }
    },
    cost_price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        isDecimal: {
          msg: 'Cost price must be a valid decimal number'
        },
        min: {
          args: [0.01],
          msg: 'Cost price must be positive'
        }
      }
    },
    location: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        len: {
          args: [0, 100],
          msg: 'Location must be at most 100 characters'
        }
      }
    },
    warehouse: {
      type: DataTypes.STRING(50),
      allowNull: true,
      defaultValue: 'main',
      validate: {
        len: {
          args: [0, 50],
          msg: 'Warehouse must be at most 50 characters'
        }
      }
    },
    purchase_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      validate: {
        isDate: {
          msg: 'Purchase date must be a valid date'
        }
      }
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      validate: {
        len: {
          args: [0, 1000],
          msg: 'Notes must be at most 1000 characters'
        }
      }
    },
    deleted_at: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'InventoryItem',
    tableName: 'inventory_items',
    timestamps: true,
    underscored: true,
    paranoid: true, // Enable soft delete
    indexes: [
      {
        unique: true,
        fields: ['serial_number']
      },
      {
        fields: ['product_id']
      },
      {
        fields: ['container_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['condition']
      },
      {
        fields: ['warehouse']
      },
      {
        fields: ['location']
      },
      {
        fields: ['purchase_date']
      }
    ]
  });

  return InventoryItem;
};
