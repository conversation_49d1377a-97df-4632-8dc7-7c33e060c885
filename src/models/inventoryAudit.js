'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class InventoryAudit extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // InventoryAudit belongs to InventoryItem
      InventoryAudit.belongsTo(models.InventoryItem, {
        foreignKey: 'inventory_item_id',
        as: 'inventoryItem',
        onDelete: 'CASCADE'
      });

      // InventoryAudit belongs to User (who made the change)
      InventoryAudit.belongsTo(models.User, {
        foreignKey: 'changed_by',
        as: 'user',
        onDelete: 'SET NULL'
      });
    }

    /**
     * Get formatted change description
     */
    getChangeDescription() {
      if (this.old_value && this.new_value) {
        return `${this.field_name} changed from "${this.old_value}" to "${this.new_value}"`;
      } else if (this.new_value) {
        return `${this.field_name} set to "${this.new_value}"`;
      } else {
        return `${this.field_name} was modified`;
      }
    }

    /**
     * Check if this is a status change
     */
    isStatusChange() {
      return this.field_name === 'status';
    }

    /**
     * Check if this is a condition change
     */
    isConditionChange() {
      return this.field_name === 'condition';
    }
  }

  InventoryAudit.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    inventory_item_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'inventory_items',
        key: 'id'
      },
      validate: {
        notNull: {
          msg: 'Inventory item ID is required'
        },
        isInt: {
          msg: 'Inventory item ID must be an integer'
        }
      }
    },
    field_name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'Field name is required'
        },
        len: {
          args: [1, 50],
          msg: 'Field name must be between 1 and 50 characters'
        }
      }
    },
    old_value: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    new_value: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    changed_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    change_reason: {
      type: DataTypes.STRING(255),
      allowNull: true,
      validate: {
        len: {
          args: [0, 255],
          msg: 'Change reason must be at most 255 characters'
        }
      }
    },
    ip_address: {
      type: DataTypes.INET,
      allowNull: true
    },
    user_agent: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'InventoryAudit',
    tableName: 'inventory_audit',
    timestamps: true,
    underscored: true,
    updatedAt: false, // Only track creation time for audit records
    indexes: [
      {
        fields: ['inventory_item_id']
      },
      {
        fields: ['field_name']
      },
      {
        fields: ['changed_by']
      },
      {
        fields: ['created_at']
      },
      {
        fields: ['inventory_item_id', 'field_name']
      }
    ]
  });

  return InventoryAudit;
};
