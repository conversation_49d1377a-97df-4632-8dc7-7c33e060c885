const { Op, Sequelize } = require('sequelize');
const { 
  Container, 
  Product, 
  InventoryItem, 
  Customer, 
  SalesOrder, 
  OrderItem 
} = require('../models');
const {
  buildDateFilter,
  buildPagination,
  formatChartData,
  calculateInventoryTurnover,
  calculateProfitMargin,
  getInventoryAging,
  getLowStockAlerts,
  getTopSellingProducts,
  getSalesTrends,
} = require('../utils/reportUtils');
const {
  formatForExport,
  setDownloadHeaders,
  prepareDataForExport,
  generateSummary,
} = require('../utils/exportUtils');

/**
 * Get inventory report with stock levels, aging analysis, and turnover metrics
 */
const getInventoryReport = async (req, res) => {
  try {
    const { startDate, endDate, format = 'json', page = 1, limit = 50 } = req.query;
    const { offset, limit: queryLimit } = buildPagination(page, limit);
    const dateFilter = buildDateFilter(startDate, endDate);

    // Current stock levels by product
    const stockLevels = await Product.findAndCountAll({
      include: [
        {
          model: InventoryItem,
          as: 'inventoryItems',
          where: {
            status: ['available', 'reserved'],
            ...dateFilter
          },
          required: false,
          attributes: ['id', 'status', 'cost_price', 'created_at']
        }
      ],
      offset,
      limit: queryLimit,
      order: [['brand', 'ASC'], ['model', 'ASC']]
    });

    // Process stock data
    const stockData = stockLevels.rows.map(product => {
      const items = product.inventoryItems || [];
      const availableCount = items.filter(item => item.status === 'available').length;
      const reservedCount = items.filter(item => item.status === 'reserved').length;
      const totalValue = items.reduce((sum, item) => sum + parseFloat(item.cost_price || 0), 0);
      
      return {
        productId: product.id,
        brand: product.brand,
        model: product.model,
        basePrice: parseFloat(product.base_price),
        availableStock: availableCount,
        reservedStock: reservedCount,
        totalStock: availableCount + reservedCount,
        totalValue: totalValue,
        averageCost: items.length > 0 ? totalValue / items.length : 0
      };
    });

    // Get aging analysis
    const agingData = await getInventoryAging(startDate, endDate);

    // Get low stock alerts
    const lowStockAlerts = await getLowStockAlerts(10);

    // Calculate inventory turnover for top products
    const topProducts = stockData
      .sort((a, b) => b.totalStock - a.totalStock)
      .slice(0, 10);

    const turnoverData = await Promise.all(
      topProducts.map(async (product) => {
        const turnover = await calculateInventoryTurnover(product.productId, startDate, endDate);
        return {
          ...product,
          turnoverRate: turnover
        };
      })
    );

    const reportData = {
      stockLevels: stockData,
      agingAnalysis: agingData,
      lowStockAlerts,
      turnoverAnalysis: turnoverData,
      summary: {
        totalProducts: stockLevels.count,
        totalItems: stockData.reduce((sum, p) => sum + p.totalStock, 0),
        totalValue: stockData.reduce((sum, p) => sum + p.totalValue, 0),
        lowStockCount: lowStockAlerts.length
      },
      pagination: {
        page: parseInt(page),
        limit: queryLimit,
        total: stockLevels.count,
        pages: Math.ceil(stockLevels.count / queryLimit)
      },
      metadata: {
        reportType: 'inventory',
        generatedAt: new Date().toISOString(),
        dateRange: { startDate, endDate },
        filters: { format, page, limit }
      }
    };

    // Handle export formats
    if (format !== 'json') {
      const exportData = prepareDataForExport(stockData);
      const formattedExport = formatForExport(exportData, format, 'Inventory Report', 'inventory_report');
      
      setDownloadHeaders(res, formattedExport);
      return res.send(formattedExport.content);
    }

    res.json({
      success: true,
      data: reportData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Inventory report error:', error);
    res.status(500).json({
      error: 'Failed to generate inventory report',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get sales report with performance analytics and trends
 */
const getSalesReport = async (req, res) => {
  try {
    const { startDate, endDate, format = 'json', page = 1, limit = 50, period = 'day' } = req.query;
    const { offset, limit: queryLimit } = buildPagination(page, limit);
    const dateFilter = buildDateFilter(startDate, endDate, 'order_date');

    // Sales performance overview
    const salesOverview = await SalesOrder.findAll({
      where: {
        status: ['completed', 'delivered'],
        ...dateFilter
      },
      attributes: [
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'totalOrders'],
        [Sequelize.fn('SUM', Sequelize.col('total_amount')), 'totalRevenue'],
        [Sequelize.fn('AVG', Sequelize.col('total_amount')), 'averageOrderValue'],
        [Sequelize.fn('COUNT', Sequelize.fn('DISTINCT', Sequelize.col('customer_id'))), 'uniqueCustomers']
      ],
      raw: true
    });

    // Sales trends
    const salesTrends = await getSalesTrends(startDate, endDate, period);

    // Top selling products
    const topProducts = await getTopSellingProducts(startDate, endDate, 10);

    // Revenue by customer tier
    const revenueByTier = await Customer.findAll({
      include: [
        {
          model: SalesOrder,
          as: 'salesOrders',
          where: {
            status: ['completed', 'delivered'],
            ...dateFilter
          },
          attributes: []
        }
      ],
      attributes: [
        'pricing_tier',
        [Sequelize.fn('COUNT', Sequelize.col('salesOrders.id')), 'orderCount'],
        [Sequelize.fn('SUM', Sequelize.col('salesOrders.total_amount')), 'totalRevenue'],
        [Sequelize.fn('AVG', Sequelize.col('salesOrders.total_amount')), 'avgOrderValue']
      ],
      group: ['Customer.pricing_tier'],
      raw: true
    });

    // Recent orders with pagination
    const recentOrders = await SalesOrder.findAndCountAll({
      where: {
        ...dateFilter
      },
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['company_name', 'pricing_tier']
        }
      ],
      offset,
      limit: queryLimit,
      order: [['order_date', 'DESC']]
    });

    const reportData = {
      overview: salesOverview[0] || {},
      trends: formatChartData(salesTrends, 'line'),
      topProducts: formatChartData(topProducts, 'bar'),
      revenueByTier: formatChartData(revenueByTier, 'pie'),
      recentOrders: recentOrders.rows,
      summary: generateSummary(recentOrders.rows, ['total_amount']),
      pagination: {
        page: parseInt(page),
        limit: queryLimit,
        total: recentOrders.count,
        pages: Math.ceil(recentOrders.count / queryLimit)
      },
      metadata: {
        reportType: 'sales',
        generatedAt: new Date().toISOString(),
        dateRange: { startDate, endDate },
        filters: { format, page, limit, period }
      }
    };

    // Handle export formats
    if (format !== 'json') {
      const exportData = prepareDataForExport(recentOrders.rows);
      const formattedExport = formatForExport(exportData, format, 'Sales Report', 'sales_report');
      
      setDownloadHeaders(res, formattedExport);
      return res.send(formattedExport.content);
    }

    res.json({
      success: true,
      data: reportData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Sales report error:', error);
    res.status(500).json({
      error: 'Failed to generate sales report',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get container report with ROI analysis and processing efficiency
 */
const getContainerReport = async (req, res) => {
  try {
    const { startDate, endDate, format = 'json', page = 1, limit = 50 } = req.query;
    const { offset, limit: queryLimit } = buildPagination(page, limit);
    const dateFilter = buildDateFilter(startDate, endDate, 'arrival_date');

    // Container overview with profitability analysis
    const containers = await Container.findAndCountAll({
      where: dateFilter,
      include: [
        {
          model: InventoryItem,
          as: 'inventoryItems',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['brand', 'model', 'base_price']
            },
            {
              model: OrderItem,
              as: 'orderItem',
              required: false,
              include: [
                {
                  model: SalesOrder,
                  as: 'salesOrder',
                  where: { status: ['completed', 'delivered'] },
                  required: false,
                  attributes: ['order_date', 'status']
                }
              ]
            }
          ]
        }
      ],
      offset,
      limit: queryLimit,
      order: [['arrival_date', 'DESC']]
    });

    // Process container data with ROI calculations
    const containerData = containers.rows.map(container => {
      const items = container.inventoryItems || [];
      const totalCost = items.reduce((sum, item) => sum + parseFloat(item.cost_price || 0), 0);

      const soldItems = items.filter(item => item.orderItem && item.orderItem.salesOrder);
      const totalRevenue = soldItems.reduce((sum, item) => {
        return sum + (parseFloat(item.orderItem?.unit_price || 0) * parseInt(item.orderItem?.quantity || 0));
      }, 0);

      const profit = totalRevenue - totalCost;
      const profitMargin = totalRevenue > 0 ? (profit / totalRevenue) * 100 : 0;
      const roi = totalCost > 0 ? (profit / totalCost) * 100 : 0;

      // Processing efficiency metrics
      const arrivalDate = new Date(container.arrival_date);
      const completedDate = container.status === 'completed' ? new Date(container.updated_at) : null;
      const processingDays = completedDate ?
        Math.ceil((completedDate - arrivalDate) / (1000 * 60 * 60 * 24)) : null;

      return {
        containerId: container.id,
        containerNumber: container.container_number,
        arrivalDate: container.arrival_date,
        status: container.status,
        totalItems: items.length,
        soldItems: soldItems.length,
        remainingItems: items.length - soldItems.length,
        totalCost,
        totalRevenue,
        profit,
        profitMargin,
        roi,
        processingDays,
        efficiency: items.length > 0 ? (soldItems.length / items.length) * 100 : 0
      };
    });

    // Container status distribution
    const statusDistribution = await Container.findAll({
      where: dateFilter,
      attributes: [
        'status',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      group: ['status'],
      raw: true
    });

    // Processing efficiency trends
    const efficiencyTrends = containerData
      .filter(c => c.processingDays !== null)
      .map(c => ({
        containerNumber: c.containerNumber,
        processingDays: c.processingDays,
        efficiency: c.efficiency,
        arrivalDate: c.arrivalDate
      }));

    const reportData = {
      containers: containerData,
      statusDistribution: formatChartData(statusDistribution, 'pie'),
      efficiencyTrends: formatChartData(efficiencyTrends, 'line'),
      summary: {
        totalContainers: containers.count,
        totalCost: containerData.reduce((sum, c) => sum + c.totalCost, 0),
        totalRevenue: containerData.reduce((sum, c) => sum + c.totalRevenue, 0),
        totalProfit: containerData.reduce((sum, c) => sum + c.profit, 0),
        averageROI: containerData.length > 0 ?
          containerData.reduce((sum, c) => sum + c.roi, 0) / containerData.length : 0,
        averageProcessingDays: efficiencyTrends.length > 0 ?
          efficiencyTrends.reduce((sum, c) => sum + c.processingDays, 0) / efficiencyTrends.length : 0
      },
      pagination: {
        page: parseInt(page),
        limit: queryLimit,
        total: containers.count,
        pages: Math.ceil(containers.count / queryLimit)
      },
      metadata: {
        reportType: 'containers',
        generatedAt: new Date().toISOString(),
        dateRange: { startDate, endDate },
        filters: { format, page, limit }
      }
    };

    // Handle export formats
    if (format !== 'json') {
      const exportData = prepareDataForExport(containerData);
      const formattedExport = formatForExport(exportData, format, 'Container Report', 'container_report');

      setDownloadHeaders(res, formattedExport);
      return res.send(formattedExport.content);
    }

    res.json({
      success: true,
      data: reportData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Container report error:', error);
    res.status(500).json({
      error: 'Failed to generate container report',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get customer report with performance metrics and tier analysis
 */
const getCustomerReport = async (req, res) => {
  try {
    const { startDate, endDate, format = 'json', page = 1, limit = 50 } = req.query;
    const { offset, limit: queryLimit } = buildPagination(page, limit);
    const dateFilter = buildDateFilter(startDate, endDate, 'order_date');

    // Customer performance metrics
    const customers = await Customer.findAndCountAll({
      include: [
        {
          model: SalesOrder,
          as: 'salesOrders',
          where: {
            status: ['completed', 'delivered'],
            ...dateFilter
          },
          required: false,
          attributes: ['id', 'order_date', 'total_amount']
        }
      ],
      offset,
      limit: queryLimit,
      order: [['company_name', 'ASC']]
    });

    // Process customer data
    const customerData = customers.rows.map(customer => {
      const orders = customer.salesOrders || [];
      const totalSpent = orders.reduce((sum, order) => sum + parseFloat(order.total_amount), 0);
      const orderCount = orders.length;
      const avgOrderValue = orderCount > 0 ? totalSpent / orderCount : 0;
      const creditUtilization = customer.getCreditUtilization();

      // Calculate customer lifetime value (simplified)
      const firstOrderDate = orders.length > 0 ?
        new Date(Math.min(...orders.map(o => new Date(o.order_date)))) : null;
      const daysSinceFirstOrder = firstOrderDate ?
        Math.ceil((new Date() - firstOrderDate) / (1000 * 60 * 60 * 24)) : 0;
      const clv = daysSinceFirstOrder > 0 ? (totalSpent / daysSinceFirstOrder) * 365 : 0;

      return {
        customerId: customer.id,
        companyName: customer.company_name,
        contactPerson: customer.contact_person,
        email: customer.email,
        pricingTier: customer.pricing_tier,
        status: customer.status,
        creditLimit: parseFloat(customer.credit_limit),
        creditUsed: parseFloat(customer.credit_used),
        creditUtilization,
        orderCount,
        totalSpent,
        avgOrderValue,
        customerLifetimeValue: clv,
        lastOrderDate: orders.length > 0 ?
          new Date(Math.max(...orders.map(o => new Date(o.order_date)))).toISOString() : null,
        daysSinceLastOrder: orders.length > 0 ?
          Math.ceil((new Date() - new Date(Math.max(...orders.map(o => new Date(o.order_date))))) / (1000 * 60 * 60 * 24)) : null
      };
    });

    // Customer tier analysis
    const tierAnalysis = await Customer.findAll({
      include: [
        {
          model: SalesOrder,
          as: 'salesOrders',
          where: {
            status: ['completed', 'delivered'],
            ...dateFilter
          },
          required: false,
          attributes: []
        }
      ],
      attributes: [
        'pricing_tier',
        [Sequelize.fn('COUNT', Sequelize.col('Customer.id')), 'customerCount'],
        [Sequelize.fn('COUNT', Sequelize.col('salesOrders.id')), 'totalOrders'],
        [Sequelize.fn('SUM', Sequelize.col('salesOrders.total_amount')), 'totalRevenue'],
        [Sequelize.fn('AVG', Sequelize.col('salesOrders.total_amount')), 'avgOrderValue']
      ],
      group: ['Customer.pricing_tier'],
      raw: true
    });

    // Credit utilization analysis
    const creditAnalysis = customerData.reduce((acc, customer) => {
      const utilizationRange = customer.creditUtilization <= 25 ? '0-25%' :
                              customer.creditUtilization <= 50 ? '26-50%' :
                              customer.creditUtilization <= 75 ? '51-75%' :
                              customer.creditUtilization <= 90 ? '76-90%' : '90%+';

      if (!acc[utilizationRange]) {
        acc[utilizationRange] = { count: 0, totalCredit: 0, totalUsed: 0 };
      }

      acc[utilizationRange].count++;
      acc[utilizationRange].totalCredit += customer.creditLimit;
      acc[utilizationRange].totalUsed += customer.creditUsed;

      return acc;
    }, {});

    const reportData = {
      customers: customerData,
      tierAnalysis: formatChartData(tierAnalysis, 'bar'),
      creditAnalysis: formatChartData(
        Object.entries(creditAnalysis).map(([range, data]) => ({
          range,
          ...data
        })),
        'pie'
      ),
      summary: {
        totalCustomers: customers.count,
        activeCustomers: customerData.filter(c => c.orderCount > 0).length,
        totalRevenue: customerData.reduce((sum, c) => sum + c.totalSpent, 0),
        avgCustomerValue: customerData.length > 0 ?
          customerData.reduce((sum, c) => sum + c.totalSpent, 0) / customerData.length : 0,
        highRiskCustomers: customerData.filter(c => c.creditUtilization > 90).length,
        topTier: customerData.filter(c => c.pricingTier === 'platinum').length
      },
      pagination: {
        page: parseInt(page),
        limit: queryLimit,
        total: customers.count,
        pages: Math.ceil(customers.count / queryLimit)
      },
      metadata: {
        reportType: 'customers',
        generatedAt: new Date().toISOString(),
        dateRange: { startDate, endDate },
        filters: { format, page, limit }
      }
    };

    // Handle export formats
    if (format !== 'json') {
      const exportData = prepareDataForExport(customerData);
      const formattedExport = formatForExport(exportData, format, 'Customer Report', 'customer_report');

      setDownloadHeaders(res, formattedExport);
      return res.send(formattedExport.content);
    }

    res.json({
      success: true,
      data: reportData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Customer report error:', error);
    res.status(500).json({
      error: 'Failed to generate customer report',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get dashboard report with real-time key business metrics
 */
const getDashboardReport = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    const userRole = req.user.role;
    const dateFilter = buildDateFilter(startDate, endDate);

    // Base metrics available to all roles
    let dashboardData = {
      overview: {},
      charts: {},
      alerts: [],
      recentActivity: []
    };

    // Inventory overview (all roles)
    const inventoryOverview = await InventoryItem.findAll({
      where: {
        status: ['available', 'reserved'],
        ...buildDateFilter(startDate, endDate)
      },
      attributes: [
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'totalItems'],
        [Sequelize.fn('SUM', Sequelize.col('cost_price')), 'totalValue'],
        [Sequelize.literal("COUNT(CASE WHEN status = 'available' THEN 1 END)"), 'availableItems'],
        [Sequelize.literal("COUNT(CASE WHEN status = 'reserved' THEN 1 END)"), 'reservedItems']
      ],
      raw: true
    });

    dashboardData.overview.inventory = inventoryOverview[0] || {};

    // Role-based data access
    if (userRole === 'admin' || userRole === 'manager') {
      // Sales overview
      const salesOverview = await SalesOrder.findAll({
        where: {
          status: ['completed', 'delivered'],
          ...buildDateFilter(startDate, endDate, 'order_date')
        },
        attributes: [
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'totalOrders'],
          [Sequelize.fn('SUM', Sequelize.col('total_amount')), 'totalRevenue'],
          [Sequelize.fn('AVG', Sequelize.col('total_amount')), 'avgOrderValue']
        ],
        raw: true
      });

      dashboardData.overview.sales = salesOverview[0] || {};

      // Container overview
      const containerOverview = await Container.findAll({
        where: buildDateFilter(startDate, endDate, 'arrival_date'),
        attributes: [
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'totalContainers'],
          [Sequelize.literal("COUNT(CASE WHEN status = 'arrived' THEN 1 END)"), 'arrivedContainers'],
          [Sequelize.literal("COUNT(CASE WHEN status = 'processing' THEN 1 END)"), 'processingContainers'],
          [Sequelize.literal("COUNT(CASE WHEN status = 'completed' THEN 1 END)"), 'completedContainers']
        ],
        raw: true
      });

      dashboardData.overview.containers = containerOverview[0] || {};

      // Customer overview
      const customerOverview = await Customer.findAll({
        include: [
          {
            model: SalesOrder,
            as: 'salesOrders',
            where: {
              status: ['completed', 'delivered'],
              ...buildDateFilter(startDate, endDate, 'order_date')
            },
            required: false,
            attributes: []
          }
        ],
        attributes: [
          [Sequelize.fn('COUNT', Sequelize.col('Customer.id')), 'totalCustomers'],
          [Sequelize.fn('COUNT', Sequelize.col('salesOrders.id')), 'activeCustomers']
        ],
        raw: true
      });

      dashboardData.overview.customers = customerOverview[0] || {};

      // Recent sales trends (last 7 days)
      const recentTrends = await getSalesTrends(
        new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        new Date().toISOString().split('T')[0],
        'day'
      );

      dashboardData.charts.salesTrends = formatChartData(recentTrends, 'line');

      // Top products (last 30 days)
      const topProducts = await getTopSellingProducts(
        new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        new Date().toISOString().split('T')[0],
        5
      );

      dashboardData.charts.topProducts = formatChartData(topProducts, 'bar');
    }

    // Alerts and notifications (role-based)
    const alerts = [];

    // Low stock alerts (all roles)
    const lowStockItems = await getLowStockAlerts(5);
    if (lowStockItems.length > 0) {
      alerts.push({
        type: 'warning',
        title: 'Low Stock Alert',
        message: `${lowStockItems.length} products have low stock levels`,
        data: lowStockItems.slice(0, 3),
        priority: 'medium'
      });
    }

    if (userRole === 'admin' || userRole === 'manager') {
      // High credit utilization alerts
      const highCreditCustomers = await Customer.findAll({
        where: {
          [Op.and]: [
            Sequelize.literal('(credit_used / credit_limit) > 0.9'),
            { status: 'active' }
          ]
        },
        attributes: ['company_name', 'credit_limit', 'credit_used'],
        limit: 5
      });

      if (highCreditCustomers.length > 0) {
        alerts.push({
          type: 'danger',
          title: 'High Credit Utilization',
          message: `${highCreditCustomers.length} customers have high credit utilization`,
          data: highCreditCustomers,
          priority: 'high'
        });
      }

      // Pending orders alert
      const pendingOrders = await SalesOrder.count({
        where: {
          status: 'pending',
          order_date: {
            [Op.gte]: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        }
      });

      if (pendingOrders > 0) {
        alerts.push({
          type: 'info',
          title: 'Pending Orders',
          message: `${pendingOrders} orders are pending processing`,
          priority: 'medium'
        });
      }
    }

    dashboardData.alerts = alerts;

    // Recent activity (last 10 items)
    const recentActivity = [];

    // Recent orders
    if (userRole === 'admin' || userRole === 'manager') {
      const recentOrders = await SalesOrder.findAll({
        include: [
          {
            model: Customer,
            as: 'customer',
            attributes: ['company_name']
          }
        ],
        order: [['created_at', 'DESC']],
        limit: 5,
        attributes: ['id', 'status', 'total_amount', 'created_at']
      });

      recentOrders.forEach(order => {
        recentActivity.push({
          type: 'order',
          title: `Order #${order.id}`,
          description: `${order.customer.company_name} - $${order.total_amount}`,
          timestamp: order.created_at,
          status: order.status
        });
      });
    }

    // Recent inventory additions
    const recentInventory = await InventoryItem.findAll({
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['brand', 'model']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: 5,
      attributes: ['id', 'serial_number', 'status', 'created_at']
    });

    recentInventory.forEach(item => {
      recentActivity.push({
        type: 'inventory',
        title: `New Item Added`,
        description: `${item.product.brand} ${item.product.model} - ${item.serial_number}`,
        timestamp: item.created_at,
        status: item.status
      });
    });

    // Sort recent activity by timestamp
    dashboardData.recentActivity = recentActivity
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, 10);

    // Add metadata
    dashboardData.metadata = {
      reportType: 'dashboard',
      userRole,
      generatedAt: new Date().toISOString(),
      dateRange: { startDate, endDate },
      refreshInterval: 300000 // 5 minutes in milliseconds
    };

    res.json({
      success: true,
      data: dashboardData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Dashboard report error:', error);
    res.status(500).json({
      error: 'Failed to generate dashboard report',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
};

module.exports = {
  getInventoryReport,
  getSalesReport,
  getContainerReport,
  getCustomerReport,
  getDashboardReport,
};
