const { Product, InventoryItem, User, sequelize } = require('../models');
const { Op } = require('sequelize');

/**
 * Create a new product
 * @route POST /api/products
 * @access Private (Admin/Manager only)
 */
const createProduct = async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { brand, model, specifications_json, base_price, image_url } = req.body;

    // Check if product with same brand and model already exists
    const existingProduct = await Product.findOne({
      where: {
        brand: brand.trim(),
        model: model.trim()
      },
      transaction
    });

    if (existingProduct) {
      await transaction.rollback();
      return res.status(409).json({
        error: 'Product already exists',
        message: `Product with brand "${brand}" and model "${model}" already exists`,
        timestamp: new Date().toISOString(),
      });
    }

    // Create the product with audit information
    const product = await Product.create({
      brand: brand.trim(),
      model: model.trim(),
      specifications_json: specifications_json || {},
      base_price,
      image_url,
      price_history: [{
        price: parseFloat(base_price),
        date: new Date().toISOString(),
        updated_by: req.user.id,
        reason: 'Initial price'
      }]
    }, {
      user: req.user,
      transaction
    });

    await transaction.commit();

    // Fetch the created product with associations
    const createdProduct = await Product.findByPk(product.id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    res.status(201).json({
      message: 'Product created successfully',
      product: {
        id: createdProduct.id,
        brand: createdProduct.brand,
        model: createdProduct.model,
        specifications_json: createdProduct.specifications_json,
        base_price: createdProduct.base_price,
        image_url: createdProduct.image_url,
        price_history: createdProduct.price_history,
        creator: createdProduct.creator,
        created_at: createdProduct.created_at,
        updated_at: createdProduct.updated_at,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating product:', error);

    if (error.name === 'SequelizeValidationError') {
      const validationErrors = error.errors.map(err => ({
        field: err.path,
        message: err.message,
      }));

      return res.status(400).json({
        error: 'Validation error',
        message: 'Invalid product data',
        details: validationErrors,
        timestamp: new Date().toISOString(),
      });
    }

    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to create product',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Get all products with advanced filtering and search
 * @route GET /api/products
 * @access Private (All authenticated users)
 */
const getProducts = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      brand,
      model,
      min_price,
      max_price,
      sort_by = 'created_at',
      sort_order = 'desc',
      include_deleted = 'false',
      specifications
    } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = {};
    const includeClause = [
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'username', 'email']
      },
      {
        model: User,
        as: 'updater',
        attributes: ['id', 'username', 'email']
      }
    ];

    // Handle soft delete
    if (include_deleted === 'false') {
      whereClause.deleted_at = null;
    }

    // Brand filter
    if (brand) {
      whereClause.brand = {
        [Op.iLike]: `%${brand}%`
      };
    }

    // Model filter
    if (model) {
      whereClause.model = {
        [Op.iLike]: `%${model}%`
      };
    }

    // Price range filter
    if (min_price || max_price) {
      whereClause.base_price = {};
      if (min_price) {
        whereClause.base_price[Op.gte] = parseFloat(min_price);
      }
      if (max_price) {
        whereClause.base_price[Op.lte] = parseFloat(max_price);
      }
    }

    // Full-text search across brand, model, and specifications
    if (search) {
      const searchTerms = search.toLowerCase().split(' ').filter(term => term.length > 0);
      const searchConditions = [];

      for (const term of searchTerms) {
        searchConditions.push({
          [Op.or]: [
            { brand: { [Op.iLike]: `%${term}%` } },
            { model: { [Op.iLike]: `%${term}%` } },
            {
              specifications_json: {
                [Op.or]: [
                  { cpu: { [Op.iLike]: `%${term}%` } },
                  { ram: { [Op.iLike]: `%${term}%` } },
                  { storage: { [Op.iLike]: `%${term}%` } },
                  { operating_system: { [Op.iLike]: `%${term}%` } },
                  { graphics: { [Op.iLike]: `%${term}%` } }
                ]
              }
            }
          ]
        });
      }

      whereClause[Op.and] = searchConditions;
    }

    // Specifications filter
    if (specifications) {
      try {
        const specFilter = JSON.parse(specifications);
        Object.keys(specFilter).forEach(key => {
          whereClause[`specifications_json.${key}`] = {
            [Op.iLike]: `%${specFilter[key]}%`
          };
        });
      } catch (error) {
        return res.status(400).json({
          error: 'Invalid specifications filter',
          message: 'Specifications filter must be valid JSON',
          timestamp: new Date().toISOString(),
        });
      }
    }

    // Execute query with pagination
    const { count, rows: products } = await Product.findAndCountAll({
      where: whereClause,
      include: includeClause,
      limit: parseInt(limit),
      offset: offset,
      order: [[sort_by, sort_order.toUpperCase()]],
      paranoid: include_deleted === 'false'
    });

    // Get inventory summary for each product
    const productsWithSummary = await Promise.all(
      products.map(async (product) => {
        const inventorySummary = await product.getInventorySummary();
        return {
          id: product.id,
          brand: product.brand,
          model: product.model,
          specifications_json: product.specifications_json,
          base_price: product.base_price,
          image_url: product.image_url,
          inventory_summary: inventorySummary,
          creator: product.creator,
          updater: product.updater,
          created_at: product.created_at,
          updated_at: product.updated_at,
          deleted_at: product.deleted_at,
        };
      })
    );

    res.json({
      products: productsWithSummary,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total_items: count,
        total_pages: Math.ceil(count / limit),
      },
      filters: {
        search,
        brand,
        model,
        min_price,
        max_price,
        specifications,
        include_deleted,
      },
      sorting: {
        sort_by,
        sort_order,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch products',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Get a single product by ID with detailed information
 * @route GET /api/products/:id
 * @access Private (All authenticated users)
 */
const getProductById = async (req, res) => {
  try {
    const { id } = req.params;

    const product = await Product.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        },
        {
          model: User,
          as: 'updater',
          attributes: ['id', 'username', 'email']
        },
        {
          model: InventoryItem,
          as: 'inventoryItems',
          attributes: ['id', 'serial_number', 'condition', 'status', 'cost_price', 'created_at']
        }
      ],
      paranoid: false // Include soft deleted products
    });

    if (!product) {
      return res.status(404).json({
        error: 'Product not found',
        message: `Product with ID ${id} does not exist`,
        timestamp: new Date().toISOString(),
      });
    }

    // Get inventory summary
    const inventorySummary = await product.getInventorySummary();

    // Calculate price trends
    const priceHistory = product.price_history || [];
    const priceTrend = priceHistory.length > 1 ? 
      ((priceHistory[priceHistory.length - 1].price - priceHistory[0].price) / priceHistory[0].price * 100).toFixed(2) : 
      0;

    res.json({
      product: {
        id: product.id,
        brand: product.brand,
        model: product.model,
        specifications_json: product.specifications_json,
        base_price: product.base_price,
        image_url: product.image_url,
        price_history: priceHistory,
        price_trend: `${priceTrend}%`,
        inventory_summary: inventorySummary,
        inventory_items: product.inventoryItems,
        creator: product.creator,
        updater: product.updater,
        created_at: product.created_at,
        updated_at: product.updated_at,
        deleted_at: product.deleted_at,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch product',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Update a product
 * @route PUT /api/products/:id
 * @access Private (Admin/Manager only)
 */
const updateProduct = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { brand, model, specifications_json, base_price, image_url, price_update_reason } = req.body;

    const product = await Product.findByPk(id, { transaction });

    if (!product) {
      await transaction.rollback();
      return res.status(404).json({
        error: 'Product not found',
        message: `Product with ID ${id} does not exist`,
        timestamp: new Date().toISOString(),
      });
    }

    // Check if updating brand/model would create a duplicate
    if (brand || model) {
      const newBrand = brand ? brand.trim() : product.brand;
      const newModel = model ? model.trim() : product.model;

      if (newBrand !== product.brand || newModel !== product.model) {
        const existingProduct = await Product.findOne({
          where: {
            brand: newBrand,
            model: newModel,
            id: { [Op.ne]: id }
          },
          transaction
        });

        if (existingProduct) {
          await transaction.rollback();
          return res.status(409).json({
            error: 'Product already exists',
            message: `Product with brand "${newBrand}" and model "${newModel}" already exists`,
            timestamp: new Date().toISOString(),
          });
        }
      }
    }

    // Track price changes
    const oldPrice = parseFloat(product.base_price);
    const newPrice = base_price ? parseFloat(base_price) : oldPrice;

    if (newPrice !== oldPrice) {
      await product.addPriceHistory(
        newPrice,
        req.user.id,
        price_update_reason || 'Price update'
      );
    }

    // Update product fields
    const updateData = {};
    if (brand) updateData.brand = brand.trim();
    if (model) updateData.model = model.trim();
    if (specifications_json !== undefined) updateData.specifications_json = specifications_json;
    if (base_price) updateData.base_price = base_price;
    if (image_url !== undefined) updateData.image_url = image_url || null;

    await product.update(updateData, {
      user: req.user,
      transaction
    });

    await transaction.commit();

    // Fetch updated product with associations
    const updatedProduct = await Product.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        },
        {
          model: User,
          as: 'updater',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    res.json({
      message: 'Product updated successfully',
      product: {
        id: updatedProduct.id,
        brand: updatedProduct.brand,
        model: updatedProduct.model,
        specifications_json: updatedProduct.specifications_json,
        base_price: updatedProduct.base_price,
        image_url: updatedProduct.image_url,
        price_history: updatedProduct.price_history,
        creator: updatedProduct.creator,
        updater: updatedProduct.updater,
        created_at: updatedProduct.created_at,
        updated_at: updatedProduct.updated_at,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating product:', error);

    if (error.name === 'SequelizeValidationError') {
      const validationErrors = error.errors.map(err => ({
        field: err.path,
        message: err.message,
      }));

      return res.status(400).json({
        error: 'Validation error',
        message: 'Invalid product data',
        details: validationErrors,
        timestamp: new Date().toISOString(),
      });
    }

    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to update product',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Soft delete a product
 * @route DELETE /api/products/:id
 * @access Private (Admin only)
 */
const deleteProduct = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    const product = await Product.findByPk(id, { transaction });

    if (!product) {
      await transaction.rollback();
      return res.status(404).json({
        error: 'Product not found',
        message: `Product with ID ${id} does not exist`,
        timestamp: new Date().toISOString(),
      });
    }

    // Check if product has associated inventory items
    const inventoryCount = await InventoryItem.count({
      where: { product_id: id },
      transaction
    });

    if (inventoryCount > 0) {
      await transaction.rollback();
      return res.status(409).json({
        error: 'Cannot delete product',
        message: `Product has ${inventoryCount} associated inventory items. Cannot delete.`,
        timestamp: new Date().toISOString(),
      });
    }

    // Soft delete the product
    await product.destroy({ transaction });

    await transaction.commit();

    res.json({
      message: 'Product deleted successfully',
      product: {
        id: product.id,
        brand: product.brand,
        model: product.model,
        deleted_at: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting product:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to delete product',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Bulk import products
 * @route POST /api/products/bulk-import
 * @access Private (Admin/Manager only)
 */
const bulkImportProducts = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { products } = req.body;
    const results = {
      successful: [],
      failed: [],
      duplicates: [],
      total: products.length
    };

    for (let i = 0; i < products.length; i++) {
      const productData = products[i];

      try {
        // Check for duplicates
        const existingProduct = await Product.findOne({
          where: {
            brand: productData.brand.trim(),
            model: productData.model.trim()
          },
          transaction
        });

        if (existingProduct) {
          results.duplicates.push({
            index: i + 1,
            brand: productData.brand,
            model: productData.model,
            error: 'Product already exists'
          });
          continue;
        }

        // Create the product
        const product = await Product.create({
          brand: productData.brand.trim(),
          model: productData.model.trim(),
          specifications_json: productData.specifications_json || {},
          base_price: productData.base_price,
          image_url: productData.image_url,
          price_history: [{
            price: parseFloat(productData.base_price),
            date: new Date().toISOString(),
            updated_by: req.user.id,
            reason: 'Bulk import initial price'
          }]
        }, {
          user: req.user,
          transaction
        });

        results.successful.push({
          index: i + 1,
          id: product.id,
          brand: product.brand,
          model: product.model,
          base_price: product.base_price
        });

      } catch (error) {
        results.failed.push({
          index: i + 1,
          brand: productData.brand,
          model: productData.model,
          error: error.message
        });
      }
    }

    // Only commit if we have some successful imports
    if (results.successful.length > 0) {
      await transaction.commit();
    } else {
      await transaction.rollback();
    }

    const statusCode = results.successful.length > 0 ? 201 : 400;

    res.status(statusCode).json({
      message: `Bulk import completed. ${results.successful.length} products imported successfully.`,
      results: {
        summary: {
          total: results.total,
          successful: results.successful.length,
          failed: results.failed.length,
          duplicates: results.duplicates.length
        },
        successful_imports: results.successful,
        failed_imports: results.failed,
        duplicate_products: results.duplicates
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error in bulk import:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to process bulk import',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Get product analytics and performance metrics
 * @route GET /api/products/analytics
 * @access Private (Admin/Manager only)
 */
const getProductAnalytics = async (req, res) => {
  try {
    const { period = '30' } = req.query; // days
    const periodDays = parseInt(period);
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - periodDays);

    // Basic product statistics
    const totalProducts = await Product.count();
    const activeProducts = await Product.count({
      where: { deleted_at: null }
    });

    // Brand distribution
    const brandStats = await Product.findAll({
      attributes: [
        'brand',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
        [sequelize.fn('AVG', sequelize.col('base_price')), 'avg_price'],
        [sequelize.fn('MIN', sequelize.col('base_price')), 'min_price'],
        [sequelize.fn('MAX', sequelize.col('base_price')), 'max_price']
      ],
      where: { deleted_at: null },
      group: ['brand'],
      order: [[sequelize.fn('COUNT', sequelize.col('id')), 'DESC']],
      limit: 10
    });

    // Price range distribution
    const priceRanges = await sequelize.query(`
      SELECT
        CASE
          WHEN base_price < 500 THEN 'Under $500'
          WHEN base_price < 1000 THEN '$500 - $999'
          WHEN base_price < 1500 THEN '$1000 - $1499'
          WHEN base_price < 2000 THEN '$1500 - $1999'
          ELSE '$2000+'
        END as price_range,
        COUNT(*) as count,
        AVG(base_price) as avg_price
      FROM products
      WHERE deleted_at IS NULL
      GROUP BY price_range
      ORDER BY MIN(base_price)
    `, {
      type: sequelize.QueryTypes.SELECT
    });

    // Recent products (last 30 days)
    const recentProducts = await Product.count({
      where: {
        created_at: {
          [Op.gte]: startDate
        },
        deleted_at: null
      }
    });

    // Most popular specifications
    const specStats = await sequelize.query(`
      SELECT
        spec_key,
        spec_value,
        COUNT(*) as count
      FROM (
        SELECT
          key as spec_key,
          value as spec_value
        FROM products,
        jsonb_each_text(specifications_json)
        WHERE deleted_at IS NULL
      ) specs
      GROUP BY spec_key, spec_value
      HAVING COUNT(*) > 1
      ORDER BY count DESC
      LIMIT 20
    `, {
      type: sequelize.QueryTypes.SELECT
    });

    // Inventory performance by product
    const inventoryPerformance = await sequelize.query(`
      SELECT
        p.id,
        p.brand,
        p.model,
        p.base_price,
        COUNT(ii.id) as total_inventory,
        COUNT(CASE WHEN ii.status = 'available' THEN 1 END) as available_count,
        COUNT(CASE WHEN ii.status = 'sold' THEN 1 END) as sold_count,
        ROUND(
          CASE
            WHEN COUNT(ii.id) > 0
            THEN (COUNT(CASE WHEN ii.status = 'sold' THEN 1 END)::numeric / COUNT(ii.id) * 100)
            ELSE 0
          END, 2
        ) as sell_through_rate
      FROM products p
      LEFT JOIN inventory_items ii ON p.id = ii.product_id
      WHERE p.deleted_at IS NULL
      GROUP BY p.id, p.brand, p.model, p.base_price
      HAVING COUNT(ii.id) > 0
      ORDER BY sell_through_rate DESC, sold_count DESC
      LIMIT 10
    `, {
      type: sequelize.QueryTypes.SELECT
    });

    res.json({
      analytics: {
        overview: {
          total_products: totalProducts,
          active_products: activeProducts,
          deleted_products: totalProducts - activeProducts,
          recent_products: recentProducts,
          period_days: periodDays
        },
        brand_distribution: brandStats.map(stat => ({
          brand: stat.brand,
          count: parseInt(stat.dataValues.count),
          avg_price: parseFloat(stat.dataValues.avg_price).toFixed(2),
          min_price: parseFloat(stat.dataValues.min_price).toFixed(2),
          max_price: parseFloat(stat.dataValues.max_price).toFixed(2)
        })),
        price_distribution: priceRanges.map(range => ({
          price_range: range.price_range,
          count: parseInt(range.count),
          avg_price: parseFloat(range.avg_price).toFixed(2)
        })),
        popular_specifications: specStats.map(spec => ({
          specification: spec.spec_key,
          value: spec.spec_value,
          count: parseInt(spec.count)
        })),
        top_performing_products: inventoryPerformance.map(product => ({
          id: product.id,
          brand: product.brand,
          model: product.model,
          base_price: parseFloat(product.base_price).toFixed(2),
          total_inventory: parseInt(product.total_inventory),
          available_count: parseInt(product.available_count),
          sold_count: parseInt(product.sold_count),
          sell_through_rate: `${product.sell_through_rate}%`
        }))
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error fetching product analytics:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch product analytics',
      timestamp: new Date().toISOString(),
    });
  }
};

module.exports = {
  createProduct,
  getProducts,
  getProductById,
  updateProduct,
  deleteProduct,
  bulkImportProducts,
  getProductAnalytics,
};
