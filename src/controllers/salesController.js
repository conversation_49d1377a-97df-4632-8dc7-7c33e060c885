const { Op } = require('sequelize');
const { SalesOrder, OrderItem, Customer, InventoryItem, Product, sequelize } = require('../models');
const {
  generateOrderQuote,
  checkInventoryAvailability,
  reserveInventoryItems,
  releaseReservedItems,
  fulfillOrderItems,
  validateCustomerCredit,
  calculateOrderTotals
} = require('../utils/salesUtils');

/**
 * Generate order quote
 */
const generateQuote = async (req, res) => {
  try {
    const { customer_id, items } = req.body;

    const quote = await generateOrderQuote(customer_id, items);

    res.status(200).json({
      message: 'Quote generated successfully',
      quote,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Generate quote error:', error);

    if (error.message.includes('not found')) {
      return res.status(404).json({
        error: 'Not found',
        message: error.message,
        timestamp: new Date().toISOString(),
      });
    }

    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to generate quote',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Create new sales order
 */
const createSalesOrder = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { customer_id, items, notes } = req.body;

    // Validate customer exists and is active
    const customer = await Customer.findByPk(customer_id);
    if (!customer || customer.status !== 'active') {
      await transaction.rollback();
      return res.status(400).json({
        error: 'Invalid customer',
        message: 'Customer not found or inactive',
        timestamp: new Date().toISOString(),
      });
    }

    // Check inventory availability
    const availability = await checkInventoryAvailability(items);
    const unavailableItems = availability.filter(item => !item.is_available);
    
    if (unavailableItems.length > 0) {
      await transaction.rollback();
      return res.status(400).json({
        error: 'Insufficient inventory',
        message: 'Some items are not available in requested quantities',
        unavailable_items: unavailableItems,
        timestamp: new Date().toISOString(),
      });
    }

    // Get product details and calculate pricing
    const itemsWithProducts = await Promise.all(
      items.map(async (item) => {
        const product = await Product.findByPk(item.product_id);
        if (!product) {
          throw new Error(`Product not found: ${item.product_id}`);
        }
        return {
          product_id: item.product_id,
          quantity: item.quantity,
          base_price: product.base_price,
          product: product.toJSON()
        };
      })
    );

    const pricing = calculateOrderTotals(itemsWithProducts, customer.pricing_tier);

    // Validate customer credit limit
    const creditValidation = await validateCustomerCredit(customer_id, pricing.total);
    if (!creditValidation.is_within_limit) {
      await transaction.rollback();
      return res.status(400).json({
        error: 'Credit limit exceeded',
        message: 'Order total exceeds customer credit limit',
        credit_validation: creditValidation,
        timestamp: new Date().toISOString(),
      });
    }

    // Create sales order
    const salesOrder = await SalesOrder.create({
      customer_id,
      order_date: new Date(),
      status: 'pending',
      total_amount: pricing.total,
      notes
    }, { transaction });

    // Reserve inventory and create order items
    const reservedItems = await reserveInventoryItems(items, transaction);
    
    const orderItems = await Promise.all(
      pricing.items.map(async (item, index) => {
        const reservedItem = reservedItems.find(ri => ri.product_id === item.product_id);
        return await OrderItem.create({
          order_id: salesOrder.id,
          inventory_item_id: reservedItem.inventory_item_id,
          quantity: item.quantity,
          unit_price: item.unit_price
        }, { transaction });
      })
    );

    await transaction.commit();

    // Get complete order details
    const orderSummary = await salesOrder.getOrderSummary();

    res.status(201).json({
      message: 'Sales order created successfully',
      order: orderSummary,
      pricing_details: pricing,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Create sales order error:', error);

    if (error.message.includes('not found')) {
      return res.status(404).json({
        error: 'Not found',
        message: error.message,
        timestamp: new Date().toISOString(),
      });
    }

    if (error.message.includes('Insufficient inventory')) {
      return res.status(400).json({
        error: 'Inventory error',
        message: error.message,
        timestamp: new Date().toISOString(),
      });
    }

    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to create sales order',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Get sales orders with filtering and pagination
 */
const getSalesOrders = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      customer_id,
      date_from,
      date_to,
      min_amount,
      max_amount,
      sort_by = 'order_date',
      sort_order = 'DESC'
    } = req.query;

    // Build where conditions
    const whereConditions = {};

    if (status) {
      whereConditions.status = status;
    }

    if (customer_id) {
      whereConditions.customer_id = customer_id;
    }

    if (date_from || date_to) {
      whereConditions.order_date = {};
      if (date_from) {
        whereConditions.order_date[Op.gte] = new Date(date_from);
      }
      if (date_to) {
        whereConditions.order_date[Op.lte] = new Date(date_to);
      }
    }

    if (min_amount || max_amount) {
      whereConditions.total_amount = {};
      if (min_amount) {
        whereConditions.total_amount[Op.gte] = parseFloat(min_amount);
      }
      if (max_amount) {
        whereConditions.total_amount[Op.lte] = parseFloat(max_amount);
      }
    }

    // Calculate pagination
    const offset = (page - 1) * limit;

    // Get orders with associations
    const { count, rows: orders } = await SalesOrder.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'pricing_tier', 'status']
        },
        {
          model: OrderItem,
          as: 'orderItems',
          include: [{
            model: InventoryItem,
            as: 'inventoryItem',
            attributes: ['id', 'serial_number', 'status'],
            include: [{
              model: Product,
              as: 'product',
              attributes: ['id', 'brand', 'model']
            }]
          }]
        }
      ],
      order: [[sort_by, sort_order.toUpperCase()]],
      limit: parseInt(limit),
      offset: offset,
      distinct: true
    });

    // Calculate pagination metadata
    const totalPages = Math.ceil(count / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    res.status(200).json({
      message: 'Sales orders retrieved successfully',
      orders,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_orders: count,
        orders_per_page: parseInt(limit),
        has_next_page: hasNextPage,
        has_prev_page: hasPrevPage,
      },
      filters: {
        status,
        customer_id,
        date_from,
        date_to,
        min_amount,
        max_amount,
        sort_by,
        sort_order
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Get sales orders error:', error);

    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve sales orders',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Get sales order by ID
 */
const getSalesOrderById = async (req, res) => {
  try {
    const { id } = req.params;

    const order = await SalesOrder.findByPk(id, {
      include: [
        {
          model: Customer,
          as: 'customer'
        },
        {
          model: OrderItem,
          as: 'orderItems',
          include: [{
            model: InventoryItem,
            as: 'inventoryItem',
            include: [{
              model: Product,
              as: 'product'
            }]
          }]
        }
      ]
    });

    if (!order) {
      return res.status(404).json({
        error: 'Order not found',
        message: `Sales order with ID ${id} not found`,
        timestamp: new Date().toISOString(),
      });
    }

    res.status(200).json({
      message: 'Sales order retrieved successfully',
      order,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Get sales order by ID error:', error);

    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve sales order',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Update sales order status
 */
const updateOrderStatus = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { status, notes } = req.body;

    const order = await SalesOrder.findByPk(id, { transaction });

    if (!order) {
      await transaction.rollback();
      return res.status(404).json({
        error: 'Order not found',
        message: `Sales order with ID ${id} not found`,
        timestamp: new Date().toISOString(),
      });
    }

    // Check if status transition is valid
    if (!order.canTransitionTo(status)) {
      await transaction.rollback();
      return res.status(400).json({
        error: 'Invalid status transition',
        message: `Cannot transition from ${order.status} to ${status}`,
        current_status: order.status,
        requested_status: status,
        timestamp: new Date().toISOString(),
      });
    }

    // Handle special status transitions
    if (status === 'cancelled') {
      // Release reserved inventory
      const releasedCount = await releaseReservedItems(order.id, transaction);
      console.log(`Released ${releasedCount} reserved items for cancelled order ${order.id}`);
    }

    // Update order status
    await order.update({
      status,
      notes: notes || order.notes
    }, { transaction });

    await transaction.commit();

    // Get updated order details
    const updatedOrder = await order.reload({
      include: [
        {
          model: Customer,
          as: 'customer'
        },
        {
          model: OrderItem,
          as: 'orderItems',
          include: [{
            model: InventoryItem,
            as: 'inventoryItem',
            include: [{
              model: Product,
              as: 'product'
            }]
          }]
        }
      ]
    });

    res.status(200).json({
      message: 'Order status updated successfully',
      order: updatedOrder,
      status_change: {
        from: order.status,
        to: status,
        updated_by: req.user.username,
        updated_at: new Date().toISOString()
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Update order status error:', error);

    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to update order status',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Fulfill sales order (process shipment)
 */
const fulfillOrder = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { shipping_info, notes } = req.body;

    const order = await SalesOrder.findByPk(id, { transaction });

    if (!order) {
      await transaction.rollback();
      return res.status(404).json({
        error: 'Order not found',
        message: `Sales order with ID ${id} not found`,
        timestamp: new Date().toISOString(),
      });
    }

    // Check if order can be fulfilled
    if (!['confirmed', 'processing'].includes(order.status)) {
      await transaction.rollback();
      return res.status(400).json({
        error: 'Invalid order status',
        message: `Order must be confirmed or processing to fulfill. Current status: ${order.status}`,
        current_status: order.status,
        timestamp: new Date().toISOString(),
      });
    }

    // Mark inventory items as sold
    const fulfilledCount = await fulfillOrderItems(order.id, transaction);

    // Update order status to shipped
    await order.update({
      status: 'shipped',
      notes: notes || order.notes,
      shipping_info: shipping_info || null
    }, { transaction });

    await transaction.commit();

    // Get updated order details
    const updatedOrder = await order.reload({
      include: [
        {
          model: Customer,
          as: 'customer'
        },
        {
          model: OrderItem,
          as: 'orderItems',
          include: [{
            model: InventoryItem,
            as: 'inventoryItem',
            include: [{
              model: Product,
              as: 'product'
            }]
          }]
        }
      ]
    });

    res.status(200).json({
      message: 'Order fulfilled successfully',
      order: updatedOrder,
      fulfillment_details: {
        items_fulfilled: fulfilledCount,
        shipping_info,
        fulfilled_by: req.user.username,
        fulfilled_at: new Date().toISOString()
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Fulfill order error:', error);

    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fulfill order',
      timestamp: new Date().toISOString(),
    });
  }
};

module.exports = {
  generateQuote,
  createSalesOrder,
  getSalesOrders,
  getSalesOrderById,
  updateOrderStatus,
  fulfillOrder
};
