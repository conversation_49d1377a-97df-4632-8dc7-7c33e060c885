const { InventoryItem, Product, Container, InventoryAudit, User } = require('../models');
const { Op } = require('sequelize');
const { 
  validateSerialNumber, 
  validateStatusTransition, 
  generateInventorySummary,
  calculateInventoryAging,
  calculateLowStockAlerts,
  generateBarcodeData,
  generateQRCodeData
} = require('../utils/inventoryUtils');

/**
 * Create audit log entry
 */
const createAuditLog = async (inventoryItemId, fieldName, oldValue, newValue, userId, changeReason, req) => {
  try {
    await InventoryAudit.create({
      inventory_item_id: inventoryItemId,
      field_name: fieldName,
      old_value: oldValue ? String(oldValue) : null,
      new_value: newValue ? String(newValue) : null,
      changed_by: userId,
      change_reason: changeReason,
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });
  } catch (error) {
    console.error('Failed to create audit log:', error);
    // Don't throw error to avoid breaking the main operation
  }
};

/**
 * Add individual laptop unit
 * POST /api/inventory
 */
const createInventoryItem = async (req, res) => {
  try {
    const {
      product_id,
      container_id,
      serial_number,
      condition = 'new',
      status = 'available',
      cost_price,
      location,
      warehouse = 'main',
      purchase_date,
      notes
    } = req.body;

    // Validate serial number format
    const serialValidation = validateSerialNumber(serial_number);
    if (!serialValidation.valid) {
      return res.status(400).json({
        error: 'Invalid serial number',
        message: serialValidation.message,
        timestamp: new Date().toISOString(),
      });
    }

    // Check if product exists
    const product = await Product.findByPk(product_id);
    if (!product) {
      return res.status(404).json({
        error: 'Product not found',
        message: 'The specified product does not exist',
        timestamp: new Date().toISOString(),
      });
    }

    // Check if container exists
    const container = await Container.findByPk(container_id);
    if (!container) {
      return res.status(404).json({
        error: 'Container not found',
        message: 'The specified container does not exist',
        timestamp: new Date().toISOString(),
      });
    }

    // Check for duplicate serial number
    const existingItem = await InventoryItem.findOne({
      where: { serial_number }
    });

    if (existingItem) {
      return res.status(409).json({
        error: 'Duplicate serial number',
        message: 'An inventory item with this serial number already exists',
        timestamp: new Date().toISOString(),
      });
    }

    // Create inventory item
    const inventoryItem = await InventoryItem.create({
      product_id,
      container_id,
      serial_number,
      condition,
      status,
      cost_price,
      location,
      warehouse,
      purchase_date,
      notes
    });

    // Create audit log for creation
    await createAuditLog(
      inventoryItem.id,
      'created',
      null,
      'Item created',
      req.user.id,
      'Initial creation',
      req
    );

    // Fetch the created item with associations
    const createdItem = await InventoryItem.findByPk(inventoryItem.id, {
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['id', 'brand', 'model']
        },
        {
          model: Container,
          as: 'container',
          attributes: ['id', 'container_number']
        }
      ]
    });

    res.status(201).json({
      message: 'Inventory item created successfully',
      data: createdItem,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error creating inventory item:', error);

    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(409).json({
        error: 'Duplicate entry',
        message: 'Serial number must be unique',
        timestamp: new Date().toISOString(),
      });
    }

    if (error.name === 'SequelizeValidationError') {
      const validationErrors = error.errors.map(err => ({
        field: err.path,
        message: err.message
      }));

      return res.status(400).json({
        error: 'Validation error',
        message: 'Invalid input data',
        details: validationErrors,
        timestamp: new Date().toISOString(),
      });
    }

    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to create inventory item',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Get inventory items with advanced filtering
 * GET /api/inventory
 */
const getInventoryItems = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      condition,
      warehouse,
      location,
      serial_number,
      product_id,
      container_id,
      date_from,
      date_to
    } = req.query;

    // Build where clause
    const whereClause = {};

    if (status) whereClause.status = status;
    if (condition) whereClause.condition = condition;
    if (warehouse) whereClause.warehouse = warehouse;
    if (location) whereClause.location = { [Op.iLike]: `%${location}%` };
    if (serial_number) whereClause.serial_number = { [Op.iLike]: `%${serial_number}%` };
    if (product_id) whereClause.product_id = product_id;
    if (container_id) whereClause.container_id = container_id;

    // Date range filtering
    if (date_from || date_to) {
      whereClause.created_at = {};
      if (date_from) whereClause.created_at[Op.gte] = new Date(date_from);
      if (date_to) whereClause.created_at[Op.lte] = new Date(date_to);
    }

    // Calculate offset
    const offset = (page - 1) * limit;

    // Fetch inventory items
    const { count, rows: inventoryItems } = await InventoryItem.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['id', 'brand', 'model', 'base_price']
        },
        {
          model: Container,
          as: 'container',
          attributes: ['id', 'container_number', 'arrival_date']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']]
    });

    // Calculate pagination info
    const totalPages = Math.ceil(count / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    res.json({
      message: 'Inventory items retrieved successfully',
      data: inventoryItems,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_items: count,
        items_per_page: parseInt(limit),
        has_next_page: hasNextPage,
        has_prev_page: hasPrevPage
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error fetching inventory items:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch inventory items',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Get specific inventory item with full history
 * GET /api/inventory/:id
 */
const getInventoryItemById = async (req, res) => {
  try {
    const { id } = req.params;

    const inventoryItem = await InventoryItem.findByPk(id, {
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['id', 'brand', 'model', 'specifications_json', 'base_price']
        },
        {
          model: Container,
          as: 'container',
          attributes: ['id', 'container_number', 'arrival_date', 'status']
        },
        {
          model: InventoryAudit,
          as: 'auditHistory',
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['id', 'username']
            }
          ],
          order: [['created_at', 'DESC']],
          limit: 50 // Limit audit history to last 50 entries
        }
      ]
    });

    if (!inventoryItem) {
      return res.status(404).json({
        error: 'Inventory item not found',
        message: 'The specified inventory item does not exist',
        timestamp: new Date().toISOString(),
      });
    }

    // Add calculated fields
    const itemData = inventoryItem.toJSON();
    itemData.days_in_status = inventoryItem.getDaysInStatus();
    itemData.is_available = inventoryItem.isAvailable();
    itemData.barcode_data = inventoryItem.getBarcodeData();

    res.json({
      message: 'Inventory item retrieved successfully',
      data: itemData,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error fetching inventory item:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch inventory item',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Update inventory item status/condition with audit trail
 * PUT /api/inventory/:id
 */
const updateInventoryItem = async (req, res) => {
  try {
    const { id } = req.params;
    const { condition, status, location, warehouse, notes, change_reason } = req.body;

    // Find the inventory item
    const inventoryItem = await InventoryItem.findByPk(id);
    if (!inventoryItem) {
      return res.status(404).json({
        error: 'Inventory item not found',
        message: 'The specified inventory item does not exist',
        timestamp: new Date().toISOString(),
      });
    }

    // Validate status transition if status is being changed
    if (status && status !== inventoryItem.status) {
      const transitionValidation = validateStatusTransition(inventoryItem.status, status);
      if (!transitionValidation.valid) {
        return res.status(400).json({
          error: 'Invalid status transition',
          message: transitionValidation.message,
          timestamp: new Date().toISOString(),
        });
      }
    }

    // Store old values for audit logging
    const oldValues = {
      condition: inventoryItem.condition,
      status: inventoryItem.status,
      location: inventoryItem.location,
      warehouse: inventoryItem.warehouse,
      notes: inventoryItem.notes
    };

    // Update the inventory item
    const updateData = {};
    if (condition !== undefined) updateData.condition = condition;
    if (status !== undefined) updateData.status = status;
    if (location !== undefined) updateData.location = location;
    if (warehouse !== undefined) updateData.warehouse = warehouse;
    if (notes !== undefined) updateData.notes = notes;

    await inventoryItem.update(updateData);

    // Create audit logs for changed fields
    const auditPromises = [];
    Object.keys(updateData).forEach(field => {
      if (oldValues[field] !== updateData[field]) {
        auditPromises.push(
          createAuditLog(
            inventoryItem.id,
            field,
            oldValues[field],
            updateData[field],
            req.user.id,
            change_reason || `${field} updated`,
            req
          )
        );
      }
    });

    await Promise.all(auditPromises);

    // Fetch updated item with associations
    const updatedItem = await InventoryItem.findByPk(id, {
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['id', 'brand', 'model']
        },
        {
          model: Container,
          as: 'container',
          attributes: ['id', 'container_number']
        }
      ]
    });

    res.json({
      message: 'Inventory item updated successfully',
      data: updatedItem,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error updating inventory item:', error);

    if (error.name === 'SequelizeValidationError') {
      const validationErrors = error.errors.map(err => ({
        field: err.path,
        message: err.message
      }));

      return res.status(400).json({
        error: 'Validation error',
        message: 'Invalid input data',
        details: validationErrors,
        timestamp: new Date().toISOString(),
      });
    }

    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to update inventory item',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Bulk inventory operations
 * POST /api/inventory/bulk
 */
const bulkInventoryOperations = async (req, res) => {
  try {
    const { operation, items, change_reason } = req.body;

    if (!operation || !items || !Array.isArray(items)) {
      return res.status(400).json({
        error: 'Invalid request',
        message: 'Operation and items array are required',
        timestamp: new Date().toISOString(),
      });
    }

    const results = {
      successful: [],
      failed: [],
      total: items.length
    };

    switch (operation) {
      case 'status_update':
        await bulkStatusUpdate(items, change_reason, req, results);
        break;
      case 'location_update':
        await bulkLocationUpdate(items, change_reason, req, results);
        break;
      case 'import_from_container':
        await bulkImportFromContainer(items, req, results);
        break;
      default:
        return res.status(400).json({
          error: 'Invalid operation',
          message: 'Supported operations: status_update, location_update, import_from_container',
          timestamp: new Date().toISOString(),
        });
    }

    const statusCode = results.failed.length === 0 ? 200 : 207; // 207 Multi-Status

    res.status(statusCode).json({
      message: `Bulk ${operation} completed`,
      data: results,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error in bulk inventory operations:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to perform bulk operation',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Bulk status update helper
 */
const bulkStatusUpdate = async (items, changeReason, req, results) => {
  for (const item of items) {
    try {
      const { id, status } = item;

      const inventoryItem = await InventoryItem.findByPk(id);
      if (!inventoryItem) {
        results.failed.push({ id, error: 'Item not found' });
        continue;
      }

      // Validate status transition
      const transitionValidation = validateStatusTransition(inventoryItem.status, status);
      if (!transitionValidation.valid) {
        results.failed.push({ id, error: transitionValidation.message });
        continue;
      }

      const oldStatus = inventoryItem.status;
      await inventoryItem.update({ status });

      // Create audit log
      await createAuditLog(
        inventoryItem.id,
        'status',
        oldStatus,
        status,
        req.user.id,
        changeReason || 'Bulk status update',
        req
      );

      results.successful.push({ id, old_status: oldStatus, new_status: status });

    } catch (error) {
      results.failed.push({ id: item.id, error: error.message });
    }
  }
};

/**
 * Bulk location update helper
 */
const bulkLocationUpdate = async (items, changeReason, req, results) => {
  for (const item of items) {
    try {
      const { id, location, warehouse } = item;

      const inventoryItem = await InventoryItem.findByPk(id);
      if (!inventoryItem) {
        results.failed.push({ id, error: 'Item not found' });
        continue;
      }

      const oldLocation = inventoryItem.location;
      const oldWarehouse = inventoryItem.warehouse;

      const updateData = {};
      if (location !== undefined) updateData.location = location;
      if (warehouse !== undefined) updateData.warehouse = warehouse;

      await inventoryItem.update(updateData);

      // Create audit logs
      const auditPromises = [];
      if (location !== undefined && location !== oldLocation) {
        auditPromises.push(
          createAuditLog(
            inventoryItem.id,
            'location',
            oldLocation,
            location,
            req.user.id,
            changeReason || 'Bulk location update',
            req
          )
        );
      }
      if (warehouse !== undefined && warehouse !== oldWarehouse) {
        auditPromises.push(
          createAuditLog(
            inventoryItem.id,
            'warehouse',
            oldWarehouse,
            warehouse,
            req.user.id,
            changeReason || 'Bulk warehouse update',
            req
          )
        );
      }

      await Promise.all(auditPromises);

      results.successful.push({
        id,
        old_location: oldLocation,
        new_location: location,
        old_warehouse: oldWarehouse,
        new_warehouse: warehouse
      });

    } catch (error) {
      results.failed.push({ id: item.id, error: error.message });
    }
  }
};

/**
 * Bulk import from container helper
 */
const bulkImportFromContainer = async (items, req, results) => {
  for (const item of items) {
    try {
      const {
        product_id,
        container_id,
        serial_number,
        condition = 'new',
        cost_price,
        location,
        warehouse = 'main'
      } = item;

      // Validate serial number
      const serialValidation = validateSerialNumber(serial_number);
      if (!serialValidation.valid) {
        results.failed.push({ serial_number, error: serialValidation.message });
        continue;
      }

      // Check for duplicate serial number
      const existingItem = await InventoryItem.findOne({
        where: { serial_number }
      });

      if (existingItem) {
        results.failed.push({ serial_number, error: 'Duplicate serial number' });
        continue;
      }

      // Create inventory item
      const inventoryItem = await InventoryItem.create({
        product_id,
        container_id,
        serial_number,
        condition,
        cost_price,
        location,
        warehouse
      });

      // Create audit log
      await createAuditLog(
        inventoryItem.id,
        'created',
        null,
        'Item imported from container',
        req.user.id,
        'Bulk import from container',
        req
      );

      results.successful.push({
        id: inventoryItem.id,
        serial_number,
        product_id,
        container_id
      });

    } catch (error) {
      results.failed.push({
        serial_number: item.serial_number,
        error: error.message
      });
    }
  }
};

/**
 * Get inventory reports (aging, low stock alerts)
 * GET /api/inventory/reports
 */
const getInventoryReports = async (req, res) => {
  try {
    const { report_type = 'summary' } = req.query;

    switch (report_type) {
      case 'summary':
        await generateSummaryReport(req, res);
        break;
      case 'aging':
        await generateAgingReport(req, res);
        break;
      case 'low_stock':
        await generateLowStockReport(req, res);
        break;
      default:
        return res.status(400).json({
          error: 'Invalid report type',
          message: 'Supported report types: summary, aging, low_stock',
          timestamp: new Date().toISOString(),
        });
    }

  } catch (error) {
    console.error('Error generating inventory report:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to generate inventory report',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Generate summary report
 */
const generateSummaryReport = async (req, res) => {
  const inventoryItems = await InventoryItem.findAll({
    include: [
      {
        model: Product,
        as: 'product',
        attributes: ['id', 'brand', 'model']
      }
    ]
  });

  const summary = generateInventorySummary(inventoryItems);

  res.json({
    message: 'Inventory summary report generated successfully',
    data: {
      report_type: 'summary',
      generated_at: new Date().toISOString(),
      summary
    },
    timestamp: new Date().toISOString(),
  });
};

/**
 * Generate aging report
 */
const generateAgingReport = async (req, res) => {
  const inventoryItems = await InventoryItem.findAll({
    where: { status: 'available' },
    include: [
      {
        model: Product,
        as: 'product',
        attributes: ['id', 'brand', 'model']
      }
    ]
  });

  const aging = calculateInventoryAging(inventoryItems);

  res.json({
    message: 'Inventory aging report generated successfully',
    data: {
      report_type: 'aging',
      generated_at: new Date().toISOString(),
      aging_buckets: aging,
      total_items: inventoryItems.length
    },
    timestamp: new Date().toISOString(),
  });
};

/**
 * Generate low stock report
 */
const generateLowStockReport = async (req, res) => {
  const { thresholds } = req.query;

  // Parse thresholds if provided (format: "product_id:threshold,product_id:threshold")
  const customThresholds = {};
  if (thresholds) {
    thresholds.split(',').forEach(pair => {
      const [productId, threshold] = pair.split(':');
      if (productId && threshold) {
        customThresholds[productId] = parseInt(threshold);
      }
    });
  }

  // Group inventory by product
  const inventoryItems = await InventoryItem.findAll({
    include: [
      {
        model: Product,
        as: 'product',
        attributes: ['id', 'brand', 'model']
      }
    ]
  });

  const inventoryByProduct = {};
  inventoryItems.forEach(item => {
    const productId = item.product_id;
    if (!inventoryByProduct[productId]) {
      inventoryByProduct[productId] = [];
    }
    inventoryByProduct[productId].push(item);
  });

  const alerts = calculateLowStockAlerts(inventoryByProduct, customThresholds);

  res.json({
    message: 'Low stock report generated successfully',
    data: {
      report_type: 'low_stock',
      generated_at: new Date().toISOString(),
      alerts,
      total_products_checked: Object.keys(inventoryByProduct).length,
      products_with_alerts: alerts.length
    },
    timestamp: new Date().toISOString(),
  });
};

/**
 * Generate barcode/QR code for inventory item
 * POST /api/inventory/:id/barcode
 */
const generateItemBarcode = async (req, res) => {
  try {
    const { id } = req.params;
    const { type = 'barcode' } = req.body;

    const inventoryItem = await InventoryItem.findByPk(id, {
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['id', 'brand', 'model']
        }
      ]
    });

    if (!inventoryItem) {
      return res.status(404).json({
        error: 'Inventory item not found',
        message: 'The specified inventory item does not exist',
        timestamp: new Date().toISOString(),
      });
    }

    let codeData;
    if (type === 'qr') {
      codeData = {
        type: 'qr_code',
        data: generateQRCodeData(inventoryItem),
        format: 'json'
      };
    } else {
      codeData = {
        type: 'barcode',
        data: generateBarcodeData(inventoryItem),
        format: 'object'
      };
    }

    res.json({
      message: `${type === 'qr' ? 'QR code' : 'Barcode'} generated successfully`,
      data: {
        inventory_item_id: inventoryItem.id,
        serial_number: inventoryItem.serial_number,
        code_data: codeData
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error generating barcode:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to generate barcode',
      timestamp: new Date().toISOString(),
    });
  }
};

module.exports = {
  createInventoryItem,
  getInventoryItems,
  getInventoryItemById,
  updateInventoryItem,
  bulkInventoryOperations,
  getInventoryReports,
  generateItemBarcode,
};
