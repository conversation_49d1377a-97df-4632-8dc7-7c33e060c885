const { User } = require('../models');
const { Op } = require('sequelize');

/**
 * Get all users (admin/manager only)
 */
const getAllUsers = async (req, res) => {
  try {
    const { page = 1, limit = 10, role, is_active, search } = req.query;
    const offset = (page - 1) * limit;

    // Build where clause
    const whereClause = {};
    
    if (role) {
      whereClause.role = role;
    }
    
    if (is_active !== undefined) {
      whereClause.is_active = is_active === 'true';
    }
    
    if (search) {
      whereClause[Op.or] = [
        { username: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } }
      ];
    }

    const { count, rows: users } = await User.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']],
      attributes: { exclude: ['password_hash', 'password_reset_token', 'password_reset_expires'] }
    });

    res.json({
      users,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit),
      },
    });
  } catch (error) {
    console.error('Get all users error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve users',
    });
  }
};

/**
 * Get user by ID
 */
const getUserById = async (req, res) => {
  try {
    const { id } = req.params;
    const requestingUser = req.user;

    // Check if user is trying to access their own profile or has admin/manager role
    if (id !== requestingUser.id && !['admin', 'manager'].includes(requestingUser.role)) {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'You can only access your own profile',
      });
    }

    const user = await User.findByPk(id, {
      attributes: { exclude: ['password_hash', 'password_reset_token', 'password_reset_expires'] }
    });

    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'User with the specified ID does not exist',
      });
    }

    res.json({ user });
  } catch (error) {
    console.error('Get user by ID error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve user',
    });
  }
};

/**
 * Update user
 */
const updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const { username, email, role, is_active } = req.body;
    const requestingUser = req.user;

    // Check permissions
    const isOwnProfile = id === requestingUser.id;
    const isAdmin = requestingUser.role === 'admin';
    const isManager = requestingUser.role === 'manager';

    if (!isOwnProfile && !isAdmin && !isManager) {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'You can only update your own profile',
      });
    }

    // Find user to update
    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'User with the specified ID does not exist',
      });
    }

    // Prepare update data
    const updateData = {};
    
    if (username !== undefined) updateData.username = username;
    if (email !== undefined) updateData.email = email;

    // Only admins can change roles and active status
    if (isAdmin) {
      if (role !== undefined) updateData.role = role;
      if (is_active !== undefined) updateData.is_active = is_active;
    } else if ((role !== undefined || is_active !== undefined) && !isAdmin) {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Only administrators can change user roles or active status',
      });
    }

    // Managers cannot modify admin users
    if (isManager && !isAdmin && user.role === 'admin') {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Managers cannot modify administrator accounts',
      });
    }

    // Update user
    await user.update(updateData);

    res.json({
      message: 'User updated successfully',
      user: user.toSafeObject(),
    });
  } catch (error) {
    console.error('Update user error:', error);
    
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        error: 'Validation error',
        message: error.errors.map(err => err.message).join(', '),
      });
    }

    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(400).json({
        error: 'Duplicate entry',
        message: 'Username or email already exists',
      });
    }

    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to update user',
    });
  }
};

/**
 * Deactivate user (admin only)
 */
const deactivateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const requestingUser = req.user;

    // Only admins can deactivate users
    if (requestingUser.role !== 'admin') {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Only administrators can deactivate users',
      });
    }

    // Prevent self-deactivation
    if (id === requestingUser.id) {
      return res.status(400).json({
        error: 'Bad request',
        message: 'You cannot deactivate your own account',
      });
    }

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'User with the specified ID does not exist',
      });
    }

    // Deactivate user
    await user.update({ is_active: false });

    res.json({
      message: 'User deactivated successfully',
      user: user.toSafeObject(),
    });
  } catch (error) {
    console.error('Deactivate user error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to deactivate user',
    });
  }
};

/**
 * Change password
 */
const changePassword = async (req, res) => {
  try {
    const { id } = req.params;
    const { currentPassword, newPassword } = req.body;
    const requestingUser = req.user;

    // Users can only change their own password
    if (id !== requestingUser.id) {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'You can only change your own password',
      });
    }

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        message: 'User with the specified ID does not exist',
      });
    }

    // Verify current password
    const isCurrentPasswordValid = await user.checkPassword(currentPassword);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        error: 'Invalid password',
        message: 'Current password is incorrect',
      });
    }

    // Update password
    await user.update({ password_hash: newPassword });

    res.json({
      message: 'Password changed successfully',
    });
  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to change password',
    });
  }
};

/**
 * Get user statistics (admin/manager only)
 */
const getUserStats = async (req, res) => {
  try {
    const stats = await User.findAll({
      attributes: [
        'role',
        [User.sequelize.fn('COUNT', User.sequelize.col('id')), 'count']
      ],
      where: { is_active: true },
      group: ['role'],
      raw: true
    });

    const totalUsers = await User.count();
    const activeUsers = await User.count({ where: { is_active: true } });
    const inactiveUsers = totalUsers - activeUsers;

    res.json({
      totalUsers,
      activeUsers,
      inactiveUsers,
      roleDistribution: stats,
    });
  } catch (error) {
    console.error('Get user stats error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve user statistics',
    });
  }
};

module.exports = {
  getAllUsers,
  getUserById,
  updateUser,
  deactivateUser,
  changePassword,
  getUserStats,
};
