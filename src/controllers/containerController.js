const { Container, InventoryItem, Product, sequelize } = require('../models');
const { Op } = require('sequelize');

/**
 * Create a new container
 */
const createContainer = async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { container_number, arrival_date, status, total_items } = req.body;

    // Check if container number already exists
    const existingContainer = await Container.findOne({
      where: { container_number },
      paranoid: false, // Check even soft-deleted containers
    });

    if (existingContainer) {
      await transaction.rollback();
      return res.status(409).json({
        error: 'Container already exists',
        message: `Container with number ${container_number} already exists`,
        timestamp: new Date().toISOString(),
      });
    }

    // Create container
    const container = await Container.create({
      container_number,
      arrival_date,
      status: status || 'arrived',
      total_items: total_items || 0,
      created_by: req.user.id,
      updated_by: req.user.id,
    }, { transaction });

    await transaction.commit();

    res.status(201).json({
      message: 'Container created successfully',
      container: {
        id: container.id,
        container_number: container.container_number,
        arrival_date: container.arrival_date,
        status: container.status,
        total_items: container.total_items,
        created_at: container.created_at,
        updated_at: container.updated_at,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Create container error:', error);
    
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(409).json({
        error: 'Container already exists',
        message: 'Container number must be unique',
        timestamp: new Date().toISOString(),
      });
    }

    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to create container',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Get all containers with filtering and pagination
 */
const getAllContainers = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      container_number,
      date_from,
      date_to,
    } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = {};

    // Apply filters
    if (status) {
      whereClause.status = status;
    }

    if (container_number) {
      whereClause.container_number = {
        [Op.iLike]: `%${container_number}%`,
      };
    }

    if (date_from || date_to) {
      whereClause.arrival_date = {};
      if (date_from) {
        whereClause.arrival_date[Op.gte] = new Date(date_from);
      }
      if (date_to) {
        whereClause.arrival_date[Op.lte] = new Date(date_to);
      }
    }

    const { count, rows: containers } = await Container.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: InventoryItem,
          as: 'inventoryItems',
          attributes: ['id', 'status', 'condition', 'cost_price'],
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'brand', 'model', 'base_price'],
            },
          ],
        },
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']],
    });

    // Calculate summary data for each container
    const containersWithSummary = containers.map(container => {
      const inventoryItems = container.inventoryItems || [];
      const totalValue = inventoryItems.reduce((sum, item) => sum + parseFloat(item.cost_price || 0), 0);
      const actualItemCount = inventoryItems.length;
      
      const statusCounts = inventoryItems.reduce((counts, item) => {
        counts[item.status] = (counts[item.status] || 0) + 1;
        return counts;
      }, {});

      return {
        id: container.id,
        container_number: container.container_number,
        arrival_date: container.arrival_date,
        status: container.status,
        total_items: container.total_items,
        actual_item_count: actualItemCount,
        total_value: totalValue.toFixed(2),
        status_breakdown: statusCounts,
        created_at: container.created_at,
        updated_at: container.updated_at,
      };
    });

    res.json({
      containers: containersWithSummary,
      pagination: {
        current_page: parseInt(page),
        per_page: parseInt(limit),
        total_items: count,
        total_pages: Math.ceil(count / limit),
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Get containers error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve containers',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Get container by ID with detailed information
 */
const getContainerById = async (req, res) => {
  try {
    const { id } = req.params;

    const container = await Container.findByPk(id, {
      include: [
        {
          model: InventoryItem,
          as: 'inventoryItems',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'brand', 'model', 'specifications_json', 'base_price'],
            },
          ],
        },
      ],
    });

    if (!container) {
      return res.status(404).json({
        error: 'Container not found',
        message: `Container with ID ${id} not found`,
        timestamp: new Date().toISOString(),
      });
    }

    // Calculate detailed summary
    const inventoryItems = container.inventoryItems || [];
    const totalValue = inventoryItems.reduce((sum, item) => sum + parseFloat(item.cost_price || 0), 0);
    
    const statusCounts = inventoryItems.reduce((counts, item) => {
      counts[item.status] = (counts[item.status] || 0) + 1;
      return counts;
    }, {});

    const conditionCounts = inventoryItems.reduce((counts, item) => {
      counts[item.condition] = (counts[item.condition] || 0) + 1;
      return counts;
    }, {});

    res.json({
      container: {
        id: container.id,
        container_number: container.container_number,
        arrival_date: container.arrival_date,
        status: container.status,
        total_items: container.total_items,
        actual_item_count: inventoryItems.length,
        total_value: totalValue.toFixed(2),
        status_breakdown: statusCounts,
        condition_breakdown: conditionCounts,
        inventory_items: inventoryItems.map(item => ({
          id: item.id,
          serial_number: item.serial_number,
          condition: item.condition,
          status: item.status,
          cost_price: item.cost_price,
          product: item.product,
          created_at: item.created_at,
        })),
        created_at: container.created_at,
        updated_at: container.updated_at,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Get container by ID error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve container',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Update container with status workflow validation
 */
const updateContainer = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { container_number, arrival_date, status, total_items } = req.body;

    const container = await Container.findByPk(id, { transaction });

    if (!container) {
      await transaction.rollback();
      return res.status(404).json({
        error: 'Container not found',
        message: `Container with ID ${id} not found`,
        timestamp: new Date().toISOString(),
      });
    }

    // Validate status workflow: arrived → processing → completed
    if (status && status !== container.status) {
      const validTransitions = {
        'arrived': ['processing'],
        'processing': ['completed'],
        'completed': [], // No transitions from completed
      };

      const allowedStatuses = validTransitions[container.status] || [];
      if (!allowedStatuses.includes(status)) {
        await transaction.rollback();
        return res.status(400).json({
          error: 'Invalid status transition',
          message: `Cannot change status from ${container.status} to ${status}. Valid transitions: ${allowedStatuses.join(', ') || 'none'}`,
          timestamp: new Date().toISOString(),
        });
      }
    }

    // Check for unique container number if being updated
    if (container_number && container_number !== container.container_number) {
      const existingContainer = await Container.findOne({
        where: {
          container_number,
          id: { [Op.ne]: id }
        },
        paranoid: false,
        transaction,
      });

      if (existingContainer) {
        await transaction.rollback();
        return res.status(409).json({
          error: 'Container number already exists',
          message: `Container with number ${container_number} already exists`,
          timestamp: new Date().toISOString(),
        });
      }
    }

    // Update container
    const updateData = {
      updated_by: req.user.id,
    };

    if (container_number !== undefined) updateData.container_number = container_number;
    if (arrival_date !== undefined) updateData.arrival_date = arrival_date;
    if (status !== undefined) updateData.status = status;
    if (total_items !== undefined) updateData.total_items = total_items;

    await container.update(updateData, { transaction });

    await transaction.commit();

    res.json({
      message: 'Container updated successfully',
      container: {
        id: container.id,
        container_number: container.container_number,
        arrival_date: container.arrival_date,
        status: container.status,
        total_items: container.total_items,
        updated_at: container.updated_at,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Update container error:', error);

    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(409).json({
        error: 'Container number already exists',
        message: 'Container number must be unique',
        timestamp: new Date().toISOString(),
      });
    }

    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to update container',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Soft delete container
 */
const deleteContainer = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    const container = await Container.findByPk(id, { transaction });

    if (!container) {
      await transaction.rollback();
      return res.status(404).json({
        error: 'Container not found',
        message: `Container with ID ${id} not found`,
        timestamp: new Date().toISOString(),
      });
    }

    // Check if container has inventory items
    const inventoryCount = await InventoryItem.count({
      where: { container_id: id },
      transaction,
    });

    if (inventoryCount > 0) {
      await transaction.rollback();
      return res.status(400).json({
        error: 'Cannot delete container',
        message: `Container has ${inventoryCount} inventory items. Please remove or reassign items before deleting.`,
        timestamp: new Date().toISOString(),
      });
    }

    // Soft delete the container
    await container.destroy({ transaction });

    await transaction.commit();

    res.json({
      message: 'Container deleted successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Delete container error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to delete container',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Bulk import inventory items to container
 */
const bulkImportInventory = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { items } = req.body;

    const container = await Container.findByPk(id, { transaction });

    if (!container) {
      await transaction.rollback();
      return res.status(404).json({
        error: 'Container not found',
        message: `Container with ID ${id} not found`,
        timestamp: new Date().toISOString(),
      });
    }

    // Validate that all products exist
    const productIds = [...new Set(items.map(item => item.product_id))];
    const existingProducts = await Product.findAll({
      where: { id: productIds },
      attributes: ['id'],
      transaction,
    });

    const existingProductIds = existingProducts.map(p => p.id);
    const missingProductIds = productIds.filter(id => !existingProductIds.includes(id));

    if (missingProductIds.length > 0) {
      await transaction.rollback();
      return res.status(400).json({
        error: 'Invalid products',
        message: `Products not found: ${missingProductIds.join(', ')}`,
        timestamp: new Date().toISOString(),
      });
    }

    // Check for duplicate serial numbers in the request
    const serialNumbers = items.map(item => item.serial_number);
    const duplicateSerials = serialNumbers.filter((serial, index) => serialNumbers.indexOf(serial) !== index);

    if (duplicateSerials.length > 0) {
      await transaction.rollback();
      return res.status(400).json({
        error: 'Duplicate serial numbers',
        message: `Duplicate serial numbers in request: ${[...new Set(duplicateSerials)].join(', ')}`,
        timestamp: new Date().toISOString(),
      });
    }

    // Check for existing serial numbers in database
    const existingItems = await InventoryItem.findAll({
      where: { serial_number: serialNumbers },
      attributes: ['serial_number'],
      paranoid: false,
      transaction,
    });

    if (existingItems.length > 0) {
      const existingSerials = existingItems.map(item => item.serial_number);
      await transaction.rollback();
      return res.status(409).json({
        error: 'Serial numbers already exist',
        message: `Serial numbers already in use: ${existingSerials.join(', ')}`,
        timestamp: new Date().toISOString(),
      });
    }

    // Create inventory items
    const inventoryItems = items.map(item => ({
      product_id: item.product_id,
      container_id: id,
      serial_number: item.serial_number,
      condition: item.condition || 'new',
      status: 'available',
      cost_price: item.cost_price,
    }));

    const createdItems = await InventoryItem.bulkCreate(inventoryItems, {
      transaction,
      returning: true,
    });

    // Update container total_items count
    await container.update({
      total_items: container.total_items + createdItems.length,
      updated_by: req.user.id,
    }, { transaction });

    await transaction.commit();

    res.status(201).json({
      message: 'Inventory items imported successfully',
      imported_count: createdItems.length,
      container: {
        id: container.id,
        container_number: container.container_number,
        total_items: container.total_items + createdItems.length,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Bulk import error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to import inventory items',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Get container summary report
 */
const getContainerSummary = async (req, res) => {
  try {
    const { id } = req.params;

    const container = await Container.findByPk(id, {
      include: [
        {
          model: InventoryItem,
          as: 'inventoryItems',
          attributes: ['id', 'status', 'condition', 'cost_price'],
        },
      ],
    });

    if (!container) {
      return res.status(404).json({
        error: 'Container not found',
        message: `Container with ID ${id} not found`,
        timestamp: new Date().toISOString(),
      });
    }

    const inventoryItems = container.inventoryItems || [];

    // Calculate summary statistics
    const totalValue = inventoryItems.reduce((sum, item) => sum + parseFloat(item.cost_price || 0), 0);
    const averageValue = inventoryItems.length > 0 ? totalValue / inventoryItems.length : 0;

    const statusBreakdown = inventoryItems.reduce((counts, item) => {
      counts[item.status] = (counts[item.status] || 0) + 1;
      return counts;
    }, {});

    const conditionBreakdown = inventoryItems.reduce((counts, item) => {
      counts[item.condition] = (counts[item.condition] || 0) + 1;
      return counts;
    }, {});

    const valueByCondition = inventoryItems.reduce((values, item) => {
      const condition = item.condition;
      values[condition] = (values[condition] || 0) + parseFloat(item.cost_price || 0);
      return values;
    }, {});

    res.json({
      summary: {
        container: {
          id: container.id,
          container_number: container.container_number,
          arrival_date: container.arrival_date,
          status: container.status,
        },
        inventory: {
          total_items: container.total_items,
          actual_item_count: inventoryItems.length,
          total_value: totalValue.toFixed(2),
          average_value: averageValue.toFixed(2),
          status_breakdown: statusBreakdown,
          condition_breakdown: conditionBreakdown,
          value_by_condition: Object.keys(valueByCondition).reduce((acc, key) => {
            acc[key] = valueByCondition[key].toFixed(2);
            return acc;
          }, {}),
        },
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Get container summary error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve container summary',
      timestamp: new Date().toISOString(),
    });
  }
};

module.exports = {
  createContainer,
  getAllContainers,
  getContainerById,
  updateContainer,
  deleteContainer,
  bulkImportInventory,
  getContainerSummary,
};
