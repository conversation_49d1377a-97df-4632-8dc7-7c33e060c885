const { Customer, CustomerCommunication, SalesOrder, OrderItem, User } = require('../models');
const { Op } = require('sequelize');
const { 
  calculateCustomerLifetimeValue,
  calculateAverageOrderValue,
  calculateOrderFrequency,
  calculateCustomerScore,
  calculatePricingTier,
  getCustomerAnalytics,
  checkPricingTierUpgrade,
  validateCustomerImportData
} = require('../utils/customerUtils');

/**
 * Create new retailer customer
 * POST /api/customers
 */
const createCustomer = async (req, res) => {
  try {
    const {
      company_name,
      contact_person,
      email,
      phone,
      address,
      credit_limit = 5000.00,
      pricing_tier = 'bronze',
      status = 'active',
      payment_terms = 30
    } = req.body;

    // Check if email already exists
    const existingCustomer = await Customer.findOne({ where: { email } });
    if (existingCustomer) {
      return res.status(409).json({
        error: 'Duplicate email',
        message: 'A customer with this email already exists',
        timestamp: new Date().toISOString(),
      });
    }

    const customer = await Customer.create({
      company_name,
      contact_person,
      email,
      phone,
      address,
      credit_limit,
      pricing_tier,
      status,
      payment_terms
    });

    // Create initial communication record
    await CustomerCommunication.create({
      customer_id: customer.id,
      type: 'note',
      subject: 'Customer Account Created',
      content: `Customer account created for ${company_name}. Contact person: ${contact_person}`,
      direction: 'outbound',
      priority: 'low',
      status: 'closed',
      communication_date: new Date(),
      created_by: req.user.id
    });

    res.status(201).json({
      message: 'Customer created successfully',
      customer: {
        id: customer.id,
        company_name: customer.company_name,
        contact_person: customer.contact_person,
        email: customer.email,
        phone: customer.phone,
        address: customer.address,
        credit_limit: customer.credit_limit,
        credit_used: customer.credit_used,
        pricing_tier: customer.pricing_tier,
        status: customer.status,
        payment_terms: customer.payment_terms,
        created_at: customer.created_at
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Create customer error:', error);
    
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(409).json({
        error: 'Duplicate entry',
        message: 'Email address must be unique',
        timestamp: new Date().toISOString(),
      });
    }

    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to create customer',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * List customers with filtering and search
 * GET /api/customers
 */
const getCustomers = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      search, 
      pricing_tier, 
      status,
      credit_limit_min,
      credit_limit_max
    } = req.query;
    
    const offset = (page - 1) * limit;

    // Build where clause
    const whereClause = {};
    
    if (pricing_tier) {
      whereClause.pricing_tier = pricing_tier;
    }
    
    if (status) {
      whereClause.status = status;
    }

    if (credit_limit_min || credit_limit_max) {
      whereClause.credit_limit = {};
      if (credit_limit_min) {
        whereClause.credit_limit[Op.gte] = credit_limit_min;
      }
      if (credit_limit_max) {
        whereClause.credit_limit[Op.lte] = credit_limit_max;
      }
    }
    
    if (search) {
      whereClause[Op.or] = [
        { company_name: { [Op.iLike]: `%${search}%` } },
        { contact_person: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } }
      ];
    }

    const { count, rows: customers } = await Customer.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']],
      attributes: req.user.role === 'employee' 
        ? { exclude: ['credit_limit', 'credit_used'] }
        : undefined
    });

    // Calculate summary statistics for admin/manager
    let summary = null;
    if (['admin', 'manager'].includes(req.user.role)) {
      const totalCreditLimit = await Customer.sum('credit_limit', { where: whereClause });
      const totalCreditUsed = await Customer.sum('credit_used', { where: whereClause });
      const avgCreditLimit = await Customer.findAll({
        where: whereClause,
        attributes: [
          [Customer.sequelize.fn('AVG', Customer.sequelize.col('credit_limit')), 'avg_credit_limit']
        ]
      });

      summary = {
        total_customers: count,
        total_credit_limit: totalCreditLimit || 0,
        total_credit_used: totalCreditUsed || 0,
        avg_credit_limit: parseFloat(avgCreditLimit[0]?.dataValues?.avg_credit_limit || 0),
        credit_utilization: totalCreditLimit > 0 ? (totalCreditUsed / totalCreditLimit) * 100 : 0
      };
    }

    res.json({
      customers,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit),
      },
      summary,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Get customers error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve customers',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Get detailed customer profile
 * GET /api/customers/:id
 */
const getCustomerById = async (req, res) => {
  try {
    const { id } = req.params;

    const customer = await Customer.findByPk(id, {
      include: [
        {
          model: SalesOrder,
          as: 'salesOrders',
          limit: 5,
          order: [['order_date', 'DESC']],
          include: [{
            model: OrderItem,
            as: 'orderItems',
            attributes: ['quantity', 'unit_price']
          }]
        },
        {
          model: CustomerCommunication,
          as: 'communications',
          limit: 10,
          order: [['communication_date', 'DESC']],
          include: [{
            model: User,
            as: 'creator',
            attributes: ['username', 'email']
          }]
        }
      ]
    });

    if (!customer) {
      return res.status(404).json({
        error: 'Customer not found',
        message: 'Customer with the specified ID does not exist',
        timestamp: new Date().toISOString(),
      });
    }

    // Calculate analytics
    const [analytics, tierUpgrade] = await Promise.all([
      getCustomerAnalytics(id),
      checkPricingTierUpgrade(id)
    ]);

    // Hide sensitive data for employees
    const customerData = customer.toJSON();
    if (req.user.role === 'employee') {
      delete customerData.credit_limit;
      delete customerData.credit_used;
    }

    res.json({
      customer: {
        ...customerData,
        credit_utilization: customer.getCreditUtilization(),
        available_credit: customer.getAvailableCredit(),
        can_place_orders: customer.canPlaceOrders(),
        pricing_discount: customer.getPricingDiscount()
      },
      analytics,
      tier_upgrade_recommendation: tierUpgrade,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Get customer by ID error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve customer',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Update customer information
 * PUT /api/customers/:id
 */
const updateCustomer = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const customer = await Customer.findByPk(id);
    if (!customer) {
      return res.status(404).json({
        error: 'Customer not found',
        message: 'Customer with the specified ID does not exist',
        timestamp: new Date().toISOString(),
      });
    }

    // Check if email is being changed and if it already exists
    if (updateData.email && updateData.email !== customer.email) {
      const existingCustomer = await Customer.findOne({
        where: {
          email: updateData.email,
          id: { [Op.ne]: id }
        }
      });
      if (existingCustomer) {
        return res.status(409).json({
          error: 'Duplicate email',
          message: 'A customer with this email already exists',
          timestamp: new Date().toISOString(),
        });
      }
    }

    // Only admin/manager can modify credit limits
    if (updateData.credit_limit && !['admin', 'manager'].includes(req.user.role)) {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Only admin and manager roles can modify credit limits',
        timestamp: new Date().toISOString(),
      });
    }

    // Store original values for audit logging
    const originalValues = {
      company_name: customer.company_name,
      contact_person: customer.contact_person,
      email: customer.email,
      phone: customer.phone,
      credit_limit: customer.credit_limit,
      pricing_tier: customer.pricing_tier,
      status: customer.status,
      payment_terms: customer.payment_terms
    };

    await customer.update(updateData);

    // Log significant changes
    const changes = [];
    Object.keys(updateData).forEach(key => {
      if (originalValues[key] !== undefined && originalValues[key] !== updateData[key]) {
        changes.push(`${key}: ${originalValues[key]} → ${updateData[key]}`);
      }
    });

    if (changes.length > 0) {
      await CustomerCommunication.create({
        customer_id: id,
        type: 'note',
        subject: 'Customer Information Updated',
        content: `Customer information updated by ${req.user.username}. Changes: ${changes.join(', ')}`,
        direction: 'outbound',
        priority: 'low',
        status: 'closed',
        communication_date: new Date(),
        created_by: req.user.id
      });
    }

    res.json({
      message: 'Customer updated successfully',
      customer: {
        id: customer.id,
        company_name: customer.company_name,
        contact_person: customer.contact_person,
        email: customer.email,
        phone: customer.phone,
        address: customer.address,
        credit_limit: customer.credit_limit,
        credit_used: customer.credit_used,
        pricing_tier: customer.pricing_tier,
        status: customer.status,
        payment_terms: customer.payment_terms,
        updated_at: customer.updated_at
      },
      changes: changes.length > 0 ? changes : null,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Update customer error:', error);

    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(409).json({
        error: 'Duplicate entry',
        message: 'Email address must be unique',
        timestamp: new Date().toISOString(),
      });
    }

    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to update customer',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Get customer order history
 * GET /api/customers/:id/orders
 */
const getCustomerOrders = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      page = 1,
      limit = 20,
      status,
      date_from,
      date_to
    } = req.query;

    const offset = (page - 1) * limit;

    // Verify customer exists
    const customer = await Customer.findByPk(id);
    if (!customer) {
      return res.status(404).json({
        error: 'Customer not found',
        message: 'Customer with the specified ID does not exist',
        timestamp: new Date().toISOString(),
      });
    }

    // Build where clause for orders
    const whereClause = { customer_id: id };

    if (status) {
      whereClause.status = status;
    }

    if (date_from || date_to) {
      whereClause.order_date = {};
      if (date_from) {
        whereClause.order_date[Op.gte] = new Date(date_from);
      }
      if (date_to) {
        whereClause.order_date[Op.lte] = new Date(date_to);
      }
    }

    const { count, rows: orders } = await SalesOrder.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['order_date', 'DESC']],
      include: [{
        model: OrderItem,
        as: 'orderItems',
        attributes: ['quantity', 'unit_price', 'total_price']
      }]
    });

    // Calculate order statistics
    const totalValue = orders.reduce((sum, order) => sum + parseFloat(order.total_amount), 0);
    const avgOrderValue = orders.length > 0 ? totalValue / orders.length : 0;
    const totalItems = orders.reduce((sum, order) =>
      sum + order.orderItems.reduce((itemSum, item) => itemSum + item.quantity, 0), 0
    );

    res.json({
      orders: orders.map(order => ({
        id: order.id,
        order_date: order.order_date,
        status: order.status,
        total_amount: order.total_amount,
        item_count: order.orderItems.reduce((sum, item) => sum + item.quantity, 0),
        items: order.orderItems
      })),
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit),
      },
      statistics: {
        total_orders: count,
        total_value: totalValue,
        average_order_value: avgOrderValue,
        total_items: totalItems
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Get customer orders error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve customer orders',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Create customer communication
 * POST /api/customers/:id/communications
 */
const createCustomerCommunication = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      type,
      subject,
      content,
      direction = 'outbound',
      priority = 'medium',
      follow_up_required = false,
      follow_up_date,
      communication_date
    } = req.body;

    // Verify customer exists
    const customer = await Customer.findByPk(id);
    if (!customer) {
      return res.status(404).json({
        error: 'Customer not found',
        message: 'Customer with the specified ID does not exist',
        timestamp: new Date().toISOString(),
      });
    }

    const communication = await CustomerCommunication.create({
      customer_id: id,
      type,
      subject,
      content,
      direction,
      priority,
      follow_up_required,
      follow_up_date,
      communication_date: communication_date || new Date(),
      created_by: req.user.id
    });

    res.status(201).json({
      message: 'Communication created successfully',
      communication: {
        id: communication.id,
        type: communication.type,
        subject: communication.subject,
        content: communication.content,
        direction: communication.direction,
        priority: communication.priority,
        status: communication.status,
        follow_up_required: communication.follow_up_required,
        follow_up_date: communication.follow_up_date,
        communication_date: communication.communication_date,
        created_at: communication.created_at
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Create customer communication error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to create communication',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Bulk customer import from CSV
 * POST /api/customers/bulk-import
 */
const bulkImportCustomers = async (req, res) => {
  try {
    const { customers } = req.body;
    const results = {
      successful: [],
      failed: [],
      total: customers.length
    };

    for (let i = 0; i < customers.length; i++) {
      const customerData = customers[i];

      try {
        // Validate customer data
        const validation = validateCustomerImportData(customerData);
        if (!validation.isValid) {
          results.failed.push({
            row: i + 1,
            data: customerData,
            errors: validation.errors
          });
          continue;
        }

        // Check for duplicate email
        const existingCustomer = await Customer.findOne({
          where: { email: customerData.email }
        });
        if (existingCustomer) {
          results.failed.push({
            row: i + 1,
            data: customerData,
            errors: ['Email already exists']
          });
          continue;
        }

        // Create customer
        const customer = await Customer.create({
          company_name: customerData.company_name,
          contact_person: customerData.contact_person,
          email: customerData.email,
          phone: customerData.phone || null,
          credit_limit: customerData.credit_limit || 5000.00,
          pricing_tier: customerData.pricing_tier || 'bronze',
          payment_terms: customerData.payment_terms || 30
        });

        // Create initial communication record
        await CustomerCommunication.create({
          customer_id: customer.id,
          type: 'note',
          subject: 'Customer Bulk Import',
          content: `Customer imported via bulk import by ${req.user.username}`,
          direction: 'outbound',
          priority: 'low',
          status: 'closed',
          communication_date: new Date(),
          created_by: req.user.id
        });

        results.successful.push({
          row: i + 1,
          customer_id: customer.id,
          company_name: customer.company_name
        });
      } catch (error) {
        results.failed.push({
          row: i + 1,
          data: customerData,
          errors: [error.message]
        });
      }
    }

    res.status(201).json({
      message: 'Bulk import completed',
      results,
      summary: {
        total: results.total,
        successful: results.successful.length,
        failed: results.failed.length,
        success_rate: ((results.successful.length / results.total) * 100).toFixed(2) + '%'
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Bulk import customers error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to import customers',
      timestamp: new Date().toISOString(),
    });
  }
};

module.exports = {
  createCustomer,
  getCustomers,
  getCustomerById,
  updateCustomer,
  getCustomerOrders,
  createCustomerCommunication,
  bulkImportCustomers
};
