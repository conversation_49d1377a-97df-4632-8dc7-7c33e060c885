const { sequelize } = require('../config/database');

/**
 * Test database connection and return status
 * @returns {Promise<Object>} Connection status object
 */
const testDatabaseConnection = async () => {
  const startTime = Date.now();
  
  try {
    // Test authentication
    await sequelize.authenticate();
    
    // Test basic query execution
    const [results] = await sequelize.query('SELECT NOW() as current_time, version() as pg_version');
    const connectionTime = Date.now() - startTime;
    
    return {
      status: 'connected',
      message: 'Database connection successful',
      connectionTime: `${connectionTime}ms`,
      database: sequelize.getDatabaseName(),
      host: sequelize.config.host,
      port: sequelize.config.port,
      dialect: sequelize.getDialect(),
      version: results[0]?.pg_version || 'unknown',
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    const connectionTime = Date.now() - startTime;
    
    return {
      status: 'disconnected',
      message: 'Database connection failed',
      error: error.message,
      connectionTime: `${connectionTime}ms`,
      database: sequelize.getDatabaseName(),
      host: sequelize.config.host,
      port: sequelize.config.port,
      dialect: sequelize.getDialect(),
      timestamp: new Date().toISOString(),
    };
  }
};

/**
 * Get database health status for health checks
 * @returns {Promise<Object>} Health status object
 */
const getDatabaseHealth = async () => {
  try {
    const connectionStatus = await testDatabaseConnection();
    
    return {
      database: {
        status: connectionStatus.status,
        connection_time: connectionStatus.connectionTime,
        database_name: connectionStatus.database,
        host: connectionStatus.host,
        port: connectionStatus.port,
        dialect: connectionStatus.dialect,
        version: connectionStatus.version,
        last_check: connectionStatus.timestamp,
      }
    };
  } catch (error) {
    return {
      database: {
        status: 'error',
        error: error.message,
        last_check: new Date().toISOString(),
      }
    };
  }
};

/**
 * Gracefully close database connection
 * @returns {Promise<void>}
 */
const closeDatabaseConnection = async () => {
  try {
    await sequelize.close();
    console.log('✅ Database connection closed successfully');
  } catch (error) {
    console.error('❌ Error closing database connection:', error.message);
  }
};

module.exports = {
  testDatabaseConnection,
  getDatabaseHealth,
  closeDatabaseConnection,
  sequelize,
};
