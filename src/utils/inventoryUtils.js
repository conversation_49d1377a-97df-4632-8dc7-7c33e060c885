const crypto = require('crypto');

/**
 * Generate barcode data for inventory item
 */
const generateBarcodeData = (inventoryItem) => {
  return {
    type: 'inventory_item',
    id: inventoryItem.id,
    serial_number: inventoryItem.serial_number,
    product_id: inventoryItem.product_id,
    checksum: generateChecksum(inventoryItem)
  };
};

/**
 * Generate QR code data for inventory item
 */
const generateQRCodeData = (inventoryItem) => {
  const data = {
    type: 'laptop_inventory',
    id: inventoryItem.id,
    serial: inventoryItem.serial_number,
    product: inventoryItem.product_id,
    status: inventoryItem.status,
    condition: inventoryItem.condition,
    location: inventoryItem.location,
    warehouse: inventoryItem.warehouse,
    timestamp: new Date().toISOString()
  };

  return JSON.stringify(data);
};

/**
 * Generate checksum for inventory item
 */
const generateChecksum = (inventoryItem) => {
  const data = `${inventoryItem.id}-${inventoryItem.serial_number}-${inventoryItem.product_id}`;
  return crypto.createHash('md5').update(data).digest('hex').substring(0, 8);
};

/**
 * Validate serial number format
 */
const validateSerialNumber = (serialNumber) => {
  // Basic validation - can be customized based on manufacturer requirements
  const patterns = {
    // Common laptop serial number patterns
    general: /^[A-Z0-9]{6,20}$/i,
    dell: /^[A-Z0-9]{7}$/i,
    hp: /^[A-Z0-9]{10}$/i,
    lenovo: /^[A-Z0-9]{8}$/i,
    apple: /^[A-Z0-9]{10,12}$/i
  };

  // Check against general pattern
  if (!patterns.general.test(serialNumber)) {
    return {
      valid: false,
      message: 'Serial number must be 6-20 alphanumeric characters'
    };
  }

  return { valid: true };
};

/**
 * Calculate inventory aging
 */
const calculateInventoryAging = (inventoryItems) => {
  const now = new Date();
  const aging = {
    '0-30': 0,
    '31-60': 0,
    '61-90': 0,
    '91-180': 0,
    '180+': 0
  };

  inventoryItems.forEach(item => {
    const createdAt = new Date(item.created_at);
    const daysDiff = Math.floor((now - createdAt) / (1000 * 60 * 60 * 24));

    if (daysDiff <= 30) {
      aging['0-30']++;
    } else if (daysDiff <= 60) {
      aging['31-60']++;
    } else if (daysDiff <= 90) {
      aging['61-90']++;
    } else if (daysDiff <= 180) {
      aging['91-180']++;
    } else {
      aging['180+']++;
    }
  });

  return aging;
};

/**
 * Calculate low stock alerts
 */
const calculateLowStockAlerts = (inventoryByProduct, thresholds = {}) => {
  const alerts = [];
  const defaultThreshold = 10;

  Object.entries(inventoryByProduct).forEach(([productId, items]) => {
    const availableCount = items.filter(item => 
      item.status === 'available' && 
      ['new', 'refurbished'].includes(item.condition)
    ).length;

    const threshold = thresholds[productId] || defaultThreshold;

    if (availableCount <= threshold) {
      alerts.push({
        product_id: productId,
        available_count: availableCount,
        threshold: threshold,
        severity: availableCount === 0 ? 'critical' : availableCount <= threshold / 2 ? 'high' : 'medium'
      });
    }
  });

  return alerts;
};

/**
 * Validate status transition
 */
const validateStatusTransition = (currentStatus, newStatus) => {
  const validTransitions = {
    'available': ['reserved', 'sold'],
    'reserved': ['available', 'sold'],
    'sold': ['returned'],
    'returned': ['available', 'reserved']
  };

  const allowedTransitions = validTransitions[currentStatus] || [];
  
  if (!allowedTransitions.includes(newStatus)) {
    return {
      valid: false,
      message: `Cannot transition from ${currentStatus} to ${newStatus}. Valid transitions: ${allowedTransitions.join(', ')}`
    };
  }

  return { valid: true };
};

/**
 * Generate inventory summary statistics
 */
const generateInventorySummary = (inventoryItems) => {
  const summary = {
    total: inventoryItems.length,
    by_status: {},
    by_condition: {},
    by_warehouse: {},
    value: {
      total: 0,
      available: 0
    }
  };

  inventoryItems.forEach(item => {
    // Count by status
    summary.by_status[item.status] = (summary.by_status[item.status] || 0) + 1;
    
    // Count by condition
    summary.by_condition[item.condition] = (summary.by_condition[item.condition] || 0) + 1;
    
    // Count by warehouse
    const warehouse = item.warehouse || 'main';
    summary.by_warehouse[warehouse] = (summary.by_warehouse[warehouse] || 0) + 1;
    
    // Calculate values
    const costPrice = parseFloat(item.cost_price) || 0;
    summary.value.total += costPrice;
    
    if (item.status === 'available') {
      summary.value.available += costPrice;
    }
  });

  return summary;
};

/**
 * Format currency value
 */
const formatCurrency = (value, currency = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(value);
};

module.exports = {
  generateBarcodeData,
  generateQRCodeData,
  generateChecksum,
  validateSerialNumber,
  calculateInventoryAging,
  calculateLowStockAlerts,
  validateStatusTransition,
  generateInventorySummary,
  formatCurrency
};
