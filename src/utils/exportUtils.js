const fs = require('fs');
const path = require('path');

/**
 * Convert array of objects to CSV format
 */
const convertToCSV = (data, filename = 'report') => {
  if (!Array.isArray(data) || data.length === 0) {
    return {
      content: 'No data available',
      filename: `${filename}.csv`,
      contentType: 'text/csv'
    };
  }

  // Get headers from the first object
  const headers = Object.keys(data[0]);
  
  // Create CSV content
  const csvContent = [
    // Header row
    headers.join(','),
    // Data rows
    ...data.map(row => 
      headers.map(header => {
        const value = row[header];
        // Handle values that might contain commas or quotes
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value || '';
      }).join(',')
    )
  ].join('\n');

  return {
    content: csvContent,
    filename: `${filename}_${new Date().toISOString().split('T')[0]}.csv`,
    contentType: 'text/csv'
  };
};

/**
 * Generate PDF report (basic implementation)
 * Note: In production, you might want to use libraries like puppeteer or pdfkit
 */
const convertToPDF = (data, title = 'Report', filename = 'report') => {
  // For now, we'll create a simple HTML-to-PDF conversion
  // In a real implementation, you'd use a proper PDF library
  
  const htmlContent = generateHTMLReport(data, title);
  
  return {
    content: htmlContent,
    filename: `${filename}_${new Date().toISOString().split('T')[0]}.pdf`,
    contentType: 'application/pdf',
    isHTML: true // Flag to indicate this needs HTML-to-PDF conversion
  };
};

/**
 * Generate HTML report for PDF conversion
 */
const generateHTMLReport = (data, title) => {
  if (!Array.isArray(data) || data.length === 0) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>${title}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          h1 { color: #333; }
        </style>
      </head>
      <body>
        <h1>${title}</h1>
        <p>No data available for this report.</p>
        <p>Generated on: ${new Date().toLocaleString()}</p>
      </body>
      </html>
    `;
  }

  const headers = Object.keys(data[0]);
  
  const tableRows = data.map(row => 
    `<tr>${headers.map(header => `<td>${row[header] || ''}</td>`).join('')}</tr>`
  ).join('');

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>${title}</title>
      <style>
        body { 
          font-family: Arial, sans-serif; 
          margin: 20px; 
          color: #333;
        }
        h1 { 
          color: #2c3e50; 
          border-bottom: 2px solid #3498db;
          padding-bottom: 10px;
        }
        table { 
          width: 100%; 
          border-collapse: collapse; 
          margin-top: 20px;
        }
        th, td { 
          border: 1px solid #ddd; 
          padding: 8px; 
          text-align: left;
        }
        th { 
          background-color: #f2f2f2; 
          font-weight: bold;
        }
        tr:nth-child(even) { 
          background-color: #f9f9f9; 
        }
        .footer {
          margin-top: 30px;
          font-size: 12px;
          color: #666;
          border-top: 1px solid #ddd;
          padding-top: 10px;
        }
      </style>
    </head>
    <body>
      <h1>${title}</h1>
      <table>
        <thead>
          <tr>${headers.map(header => `<th>${header.replace(/_/g, ' ').toUpperCase()}</th>`).join('')}</tr>
        </thead>
        <tbody>
          ${tableRows}
        </tbody>
      </table>
      <div class="footer">
        <p>Generated on: ${new Date().toLocaleString()}</p>
        <p>Total records: ${data.length}</p>
      </div>
    </body>
    </html>
  `;
};

/**
 * Format data for export based on the requested format
 */
const formatForExport = (data, format, title, filename) => {
  switch (format.toLowerCase()) {
    case 'csv':
      return convertToCSV(data, filename);
    case 'pdf':
      return convertToPDF(data, title, filename);
    case 'json':
    default:
      return {
        content: JSON.stringify(data, null, 2),
        filename: `${filename}_${new Date().toISOString().split('T')[0]}.json`,
        contentType: 'application/json'
      };
  }
};

/**
 * Set appropriate response headers for file download
 */
const setDownloadHeaders = (res, exportData) => {
  res.setHeader('Content-Type', exportData.contentType);
  res.setHeader('Content-Disposition', `attachment; filename="${exportData.filename}"`);
  
  if (exportData.contentType === 'text/csv') {
    res.setHeader('Cache-Control', 'no-cache');
  }
};

/**
 * Flatten nested objects for CSV export
 */
const flattenObject = (obj, prefix = '') => {
  const flattened = {};
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];
      const newKey = prefix ? `${prefix}_${key}` : key;
      
      if (value && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
        Object.assign(flattened, flattenObject(value, newKey));
      } else if (Array.isArray(value)) {
        flattened[newKey] = value.join('; ');
      } else {
        flattened[newKey] = value;
      }
    }
  }
  
  return flattened;
};

/**
 * Prepare data for export by flattening nested structures
 */
const prepareDataForExport = (data) => {
  if (!Array.isArray(data)) {
    return [];
  }
  
  return data.map(item => {
    if (typeof item === 'object' && item !== null) {
      return flattenObject(item);
    }
    return item;
  });
};

/**
 * Generate summary statistics for reports
 */
const generateSummary = (data, numericFields = []) => {
  if (!Array.isArray(data) || data.length === 0) {
    return {
      totalRecords: 0,
      generatedAt: new Date().toISOString()
    };
  }

  const summary = {
    totalRecords: data.length,
    generatedAt: new Date().toISOString()
  };

  // Calculate statistics for numeric fields
  numericFields.forEach(field => {
    const values = data
      .map(item => parseFloat(item[field]))
      .filter(value => !isNaN(value));
    
    if (values.length > 0) {
      summary[`${field}_total`] = values.reduce((sum, val) => sum + val, 0);
      summary[`${field}_average`] = summary[`${field}_total`] / values.length;
      summary[`${field}_min`] = Math.min(...values);
      summary[`${field}_max`] = Math.max(...values);
    }
  });

  return summary;
};

module.exports = {
  convertToCSV,
  convertToPDF,
  generateHTMLReport,
  formatForExport,
  setDownloadHeaders,
  flattenObject,
  prepareDataForExport,
  generateSummary,
};
