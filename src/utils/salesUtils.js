const { Op } = require('sequelize');
const { Customer, InventoryItem, Product, SalesOrder, OrderItem } = require('../models');

/**
 * Calculate dynamic pricing based on customer tier and quantity
 */
const calculateDynamicPrice = (basePrice, customerTier, quantity) => {
  // Base pricing tiers
  const tierDiscounts = {
    'bronze': 0.00,    // No discount
    'silver': 0.05,    // 5% discount
    'gold': 0.10,      // 10% discount
    'platinum': 0.15   // 15% discount
  };

  // Volume discounts
  const volumeDiscounts = [
    { minQuantity: 100, discount: 0.10 }, // 10% for 100+
    { minQuantity: 50, discount: 0.07 },  // 7% for 50+
    { minQuantity: 20, discount: 0.05 },  // 5% for 20+
    { minQuantity: 10, discount: 0.03 },  // 3% for 10+
  ];

  let price = parseFloat(basePrice);
  
  // Apply tier discount
  const tierDiscount = tierDiscounts[customerTier] || 0;
  price = price * (1 - tierDiscount);

  // Apply volume discount (find the highest applicable discount)
  const applicableVolumeDiscount = volumeDiscounts.find(vd => quantity >= vd.minQuantity);
  if (applicableVolumeDiscount) {
    price = price * (1 - applicableVolumeDiscount.discount);
  }

  return Math.round(price * 100) / 100; // Round to 2 decimal places
};

/**
 * Check inventory availability for order items
 */
const checkInventoryAvailability = async (orderItems) => {
  const availabilityResults = [];

  for (const item of orderItems) {
    const { product_id, quantity } = item;

    // Count available inventory items for this product
    const availableCount = await InventoryItem.count({
      where: {
        product_id,
        status: 'available',
        deleted_at: null
      }
    });

    availabilityResults.push({
      product_id,
      requested_quantity: quantity,
      available_quantity: availableCount,
      is_available: availableCount >= quantity,
      shortage: Math.max(0, quantity - availableCount)
    });
  }

  return availabilityResults;
};

/**
 * Reserve inventory items for an order
 */
const reserveInventoryItems = async (orderItems, transaction) => {
  const reservedItems = [];

  for (const item of orderItems) {
    const { product_id, quantity } = item;

    // Find available inventory items for this product
    const availableItems = await InventoryItem.findAll({
      where: {
        product_id,
        status: 'available',
        deleted_at: null
      },
      limit: quantity,
      order: [['created_at', 'ASC']], // FIFO - First In, First Out
      transaction
    });

    if (availableItems.length < quantity) {
      throw new Error(`Insufficient inventory for product ${product_id}. Requested: ${quantity}, Available: ${availableItems.length}`);
    }

    // Reserve the items
    for (const inventoryItem of availableItems) {
      await inventoryItem.update({
        status: 'reserved'
      }, { transaction });

      reservedItems.push({
        product_id,
        inventory_item_id: inventoryItem.id,
        serial_number: inventoryItem.serial_number
      });
    }
  }

  return reservedItems;
};

/**
 * Release reserved inventory items (for order cancellation)
 */
const releaseReservedItems = async (orderId, transaction) => {
  // Find all order items for this order
  const orderItems = await OrderItem.findAll({
    where: { order_id: orderId },
    include: [{
      model: InventoryItem,
      as: 'inventoryItem',
      where: { status: 'reserved' }
    }],
    transaction
  });

  // Release each reserved item
  for (const orderItem of orderItems) {
    if (orderItem.inventoryItem) {
      await orderItem.inventoryItem.update({
        status: 'available'
      }, { transaction });
    }
  }

  return orderItems.length;
};

/**
 * Fulfill order items (mark as sold)
 */
const fulfillOrderItems = async (orderId, transaction) => {
  // Find all order items for this order
  const orderItems = await OrderItem.findAll({
    where: { order_id: orderId },
    include: [{
      model: InventoryItem,
      as: 'inventoryItem',
      where: { status: 'reserved' }
    }],
    transaction
  });

  // Mark each reserved item as sold
  for (const orderItem of orderItems) {
    if (orderItem.inventoryItem) {
      await orderItem.inventoryItem.update({
        status: 'sold'
      }, { transaction });
    }
  }

  return orderItems.length;
};

/**
 * Validate customer credit limit
 */
const validateCustomerCredit = async (customerId, orderTotal) => {
  const customer = await Customer.findByPk(customerId);
  
  if (!customer) {
    throw new Error('Customer not found');
  }

  // Calculate current credit usage from pending/confirmed orders
  const pendingOrdersTotal = await SalesOrder.sum('total_amount', {
    where: {
      customer_id: customerId,
      status: {
        [Op.in]: ['pending', 'confirmed', 'processing']
      },
      deleted_at: null
    }
  }) || 0;

  const totalCreditUsage = pendingOrdersTotal + orderTotal;
  const availableCredit = customer.credit_limit - customer.credit_used;

  return {
    customer,
    credit_limit: customer.credit_limit,
    credit_used: customer.credit_used,
    pending_orders_total: pendingOrdersTotal,
    order_total: orderTotal,
    total_credit_usage: totalCreditUsage,
    available_credit: availableCredit,
    is_within_limit: totalCreditUsage <= customer.credit_limit,
    credit_shortage: Math.max(0, totalCreditUsage - customer.credit_limit)
  };
};

/**
 * Calculate order totals with tax and discounts
 */
const calculateOrderTotals = (orderItems, customerTier, taxRate = 0.08) => {
  let subtotal = 0;
  let totalDiscount = 0;

  const itemsWithPricing = orderItems.map(item => {
    const basePrice = parseFloat(item.base_price || item.unit_price);
    const dynamicPrice = calculateDynamicPrice(basePrice, customerTier, item.quantity);
    const itemTotal = dynamicPrice * item.quantity;
    const itemDiscount = (basePrice - dynamicPrice) * item.quantity;

    subtotal += itemTotal;
    totalDiscount += itemDiscount;

    return {
      ...item,
      base_price: basePrice,
      unit_price: dynamicPrice,
      item_total: itemTotal,
      item_discount: itemDiscount
    };
  });

  const taxAmount = subtotal * taxRate;
  const total = subtotal + taxAmount;

  return {
    items: itemsWithPricing,
    subtotal: Math.round(subtotal * 100) / 100,
    total_discount: Math.round(totalDiscount * 100) / 100,
    tax_rate: taxRate,
    tax_amount: Math.round(taxAmount * 100) / 100,
    total: Math.round(total * 100) / 100
  };
};

/**
 * Generate order quote
 */
const generateOrderQuote = async (customerId, requestedItems) => {
  const customer = await Customer.findByPk(customerId);
  if (!customer) {
    throw new Error('Customer not found');
  }

  // Get product details for requested items
  const itemsWithProducts = await Promise.all(
    requestedItems.map(async (item) => {
      const product = await Product.findByPk(item.product_id);
      if (!product) {
        throw new Error(`Product not found: ${item.product_id}`);
      }

      return {
        product_id: item.product_id,
        quantity: item.quantity,
        base_price: product.base_price,
        product: product.toJSON()
      };
    })
  );

  // Check inventory availability
  const availability = await checkInventoryAvailability(requestedItems);

  // Calculate pricing
  const pricing = calculateOrderTotals(itemsWithProducts, customer.pricing_tier);

  // Validate credit
  const creditValidation = await validateCustomerCredit(customerId, pricing.total);

  return {
    customer: customer.toJSON(),
    items: pricing.items,
    availability,
    pricing: {
      subtotal: pricing.subtotal,
      total_discount: pricing.total_discount,
      tax_rate: pricing.tax_rate,
      tax_amount: pricing.tax_amount,
      total: pricing.total
    },
    credit_validation: creditValidation,
    quote_valid_until: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    can_fulfill: availability.every(a => a.is_available) && creditValidation.is_within_limit
  };
};

module.exports = {
  calculateDynamicPrice,
  checkInventoryAvailability,
  reserveInventoryItems,
  releaseReservedItems,
  fulfillOrderItems,
  validateCustomerCredit,
  calculateOrderTotals,
  generateOrderQuote
};
