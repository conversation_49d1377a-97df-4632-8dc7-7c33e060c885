const { Op, Sequelize } = require('sequelize');
const { 
  Container, 
  Product, 
  InventoryItem, 
  Customer, 
  SalesOrder, 
  OrderItem 
} = require('../models');

/**
 * Build date range filter for queries
 */
const buildDateFilter = (startDate, endDate, dateField = 'created_at') => {
  const filter = {};
  
  if (startDate || endDate) {
    filter[dateField] = {};
    
    if (startDate) {
      filter[dateField][Op.gte] = new Date(startDate);
    }
    
    if (endDate) {
      // Add 23:59:59 to include the entire end date
      const endDateTime = new Date(endDate);
      endDateTime.setHours(23, 59, 59, 999);
      filter[dateField][Op.lte] = endDateTime;
    }
  }
  
  return filter;
};

/**
 * Calculate pagination offset and limit
 */
const buildPagination = (page = 1, limit = 50) => {
  const offset = (page - 1) * limit;
  return { offset, limit: Math.min(limit, 1000) };
};

/**
 * Format data for chart visualization
 */
const formatChartData = (data, type = 'line') => {
  const chartConfig = {
    type,
    data,
    metadata: {
      chartType: type,
      generatedAt: new Date().toISOString(),
    }
  };

  switch (type) {
    case 'line':
    case 'bar':
      chartConfig.metadata.xAxis = 'date';
      chartConfig.metadata.yAxis = 'value';
      break;
    case 'pie':
    case 'doughnut':
      chartConfig.metadata.labelField = 'label';
      chartConfig.metadata.valueField = 'value';
      break;
    default:
      break;
  }

  return chartConfig;
};

/**
 * Calculate inventory turnover rate
 */
const calculateInventoryTurnover = async (productId, startDate, endDate) => {
  const dateFilter = buildDateFilter(startDate, endDate, 'order_date');
  
  // Get cost of goods sold (COGS)
  const soldItems = await OrderItem.findAll({
    include: [
      {
        model: SalesOrder,
        as: 'salesOrder',
        where: {
          status: ['completed', 'delivered'],
          ...dateFilter
        },
        attributes: []
      },
      {
        model: InventoryItem,
        as: 'inventoryItem',
        where: productId ? { product_id: productId } : {},
        attributes: ['cost_price']
      }
    ],
    attributes: ['quantity']
  });

  const cogs = soldItems.reduce((total, item) => {
    return total + (item.quantity * parseFloat(item.inventoryItem.cost_price));
  }, 0);

  // Get average inventory value
  const avgInventoryValue = await InventoryItem.findOne({
    where: {
      ...(productId ? { product_id: productId } : {}),
      status: ['available', 'reserved']
    },
    attributes: [
      [Sequelize.fn('AVG', Sequelize.col('cost_price')), 'avgCost'],
      [Sequelize.fn('COUNT', Sequelize.col('id')), 'totalItems']
    ]
  });

  const avgInventory = avgInventoryValue ? 
    parseFloat(avgInventoryValue.dataValues.avgCost) * parseInt(avgInventoryValue.dataValues.totalItems) : 0;

  return avgInventory > 0 ? (cogs / avgInventory) : 0;
};

/**
 * Calculate profit margin
 */
const calculateProfitMargin = (costPrice, sellingPrice) => {
  if (!costPrice || !sellingPrice || sellingPrice === 0) return 0;
  return ((sellingPrice - costPrice) / sellingPrice) * 100;
};

/**
 * Get inventory aging analysis
 */
const getInventoryAging = async (startDate, endDate) => {
  try {
    // Use Sequelize to avoid raw SQL issues
    const whereClause = {
      status: ['available', 'reserved'],
      deleted_at: null
    };

    if (startDate) {
      whereClause.created_at = { [Op.gte]: new Date(startDate) };
    }
    if (endDate) {
      const endDateTime = new Date(endDate);
      endDateTime.setHours(23, 59, 59, 999);
      whereClause.created_at = {
        ...whereClause.created_at,
        [Op.lte]: endDateTime
      };
    }

    const items = await InventoryItem.findAll({
      where: whereClause,
      attributes: ['created_at', 'cost_price']
    });

    // Process aging in JavaScript
    const agingGroups = {
      '0-30 days': { item_count: 0, total_value: 0 },
      '31-60 days': { item_count: 0, total_value: 0 },
      '61-90 days': { item_count: 0, total_value: 0 },
      '91-180 days': { item_count: 0, total_value: 0 },
      '180+ days': { item_count: 0, total_value: 0 }
    };

    const now = new Date();

    items.forEach(item => {
      const daysDiff = Math.ceil((now - new Date(item.created_at)) / (1000 * 60 * 60 * 24));
      const costPrice = parseFloat(item.cost_price) || 0;

      let ageGroup;
      if (daysDiff <= 30) ageGroup = '0-30 days';
      else if (daysDiff <= 60) ageGroup = '31-60 days';
      else if (daysDiff <= 90) ageGroup = '61-90 days';
      else if (daysDiff <= 180) ageGroup = '91-180 days';
      else ageGroup = '180+ days';

      agingGroups[ageGroup].item_count++;
      agingGroups[ageGroup].total_value += costPrice;
    });

    // Convert to array format
    return Object.entries(agingGroups).map(([age_group, data]) => ({
      age_group,
      item_count: data.item_count,
      total_value: data.total_value
    }));
  } catch (error) {
    console.error('Aging analysis error:', error);
    return [];
  }
};

/**
 * Get low stock alerts
 */
const getLowStockAlerts = async (threshold = 10) => {
  const lowStockQuery = `
    SELECT 
      p.brand,
      p.model,
      COUNT(ii.id) as current_stock,
      p.base_price,
      SUM(ii.cost_price) as total_inventory_value
    FROM products p
    LEFT JOIN inventory_items ii ON p.id = ii.product_id 
      AND ii.status IN ('available', 'reserved') 
      AND ii.deleted_at IS NULL
    WHERE p.deleted_at IS NULL
    GROUP BY p.id, p.brand, p.model, p.base_price
    HAVING COUNT(ii.id) <= $1
    ORDER BY COUNT(ii.id) ASC
  `;

  const [results] = await Product.sequelize.query(lowStockQuery, {
    bind: [threshold]
  });
  
  return results;
};

/**
 * Get top selling products
 */
const getTopSellingProducts = async (startDate, endDate, limit = 10) => {
  const dateFilter = buildDateFilter(startDate, endDate, 'order_date');
  
  const topProductsQuery = `
    SELECT 
      p.brand,
      p.model,
      SUM(oi.quantity) as total_sold,
      SUM(oi.unit_price * oi.quantity) as total_revenue,
      AVG(oi.unit_price) as avg_selling_price,
      COUNT(DISTINCT so.id) as order_count
    FROM products p
    INNER JOIN inventory_items ii ON p.id = ii.product_id
    INNER JOIN order_items oi ON ii.id = oi.inventory_item_id
    INNER JOIN sales_orders so ON oi.order_id = so.id
    WHERE so.status IN ('completed', 'delivered')
      AND so.deleted_at IS NULL
      ${startDate ? `AND so.order_date >= '${startDate}'` : ''}
      ${endDate ? `AND so.order_date <= '${endDate} 23:59:59'` : ''}
    GROUP BY p.id, p.brand, p.model
    ORDER BY total_sold DESC
    LIMIT $1
  `;

  const [results] = await Product.sequelize.query(topProductsQuery, {
    bind: [limit]
  });
  
  return results;
};

/**
 * Get sales trends by period
 */
const getSalesTrends = async (startDate, endDate, period = 'day') => {
  const dateFilter = buildDateFilter(startDate, endDate, 'order_date');
  
  let dateFormat;
  switch (period) {
    case 'hour':
      dateFormat = "DATE_TRUNC('hour', order_date)";
      break;
    case 'day':
      dateFormat = "DATE_TRUNC('day', order_date)";
      break;
    case 'week':
      dateFormat = "DATE_TRUNC('week', order_date)";
      break;
    case 'month':
      dateFormat = "DATE_TRUNC('month', order_date)";
      break;
    default:
      dateFormat = "DATE_TRUNC('day', order_date)";
  }

  const trendsQuery = `
    SELECT 
      ${dateFormat} as period,
      COUNT(*) as order_count,
      SUM(total_amount) as total_revenue,
      AVG(total_amount) as avg_order_value,
      COUNT(DISTINCT customer_id) as unique_customers
    FROM sales_orders
    WHERE status IN ('completed', 'delivered')
      AND deleted_at IS NULL
      ${startDate ? `AND order_date >= '${startDate}'` : ''}
      ${endDate ? `AND order_date <= '${endDate} 23:59:59'` : ''}
    GROUP BY ${dateFormat}
    ORDER BY period ASC
  `;

  const [results] = await SalesOrder.sequelize.query(trendsQuery);
  return results;
};

module.exports = {
  buildDateFilter,
  buildPagination,
  formatChartData,
  calculateInventoryTurnover,
  calculateProfitMargin,
  getInventoryAging,
  getLowStockAlerts,
  getTopSellingProducts,
  getSalesTrends,
};
