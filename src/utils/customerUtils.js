const { Op } = require('sequelize');
const { SalesOrder, OrderItem, Customer, CustomerCommunication } = require('../models');

/**
 * Calculate customer lifetime value
 */
const calculateCustomerLifetimeValue = async (customerId) => {
  try {
    const orders = await SalesOrder.findAll({
      where: { 
        customer_id: customerId,
        status: { [Op.in]: ['confirmed', 'shipped', 'delivered'] }
      },
      attributes: ['total_amount']
    });

    return orders.reduce((total, order) => total + parseFloat(order.total_amount), 0);
  } catch (error) {
    console.error('Error calculating customer lifetime value:', error);
    return 0;
  }
};

/**
 * Calculate average order value for a customer
 */
const calculateAverageOrderValue = async (customerId) => {
  try {
    const orders = await SalesOrder.findAll({
      where: { 
        customer_id: customerId,
        status: { [Op.in]: ['confirmed', 'shipped', 'delivered'] }
      },
      attributes: ['total_amount']
    });

    if (orders.length === 0) return 0;
    
    const totalValue = orders.reduce((total, order) => total + parseFloat(order.total_amount), 0);
    return totalValue / orders.length;
  } catch (error) {
    console.error('Error calculating average order value:', error);
    return 0;
  }
};

/**
 * Get customer order frequency (orders per month)
 */
const calculateOrderFrequency = async (customerId) => {
  try {
    const firstOrder = await SalesOrder.findOne({
      where: { customer_id: customerId },
      order: [['order_date', 'ASC']],
      attributes: ['order_date']
    });

    if (!firstOrder) return 0;

    const totalOrders = await SalesOrder.count({
      where: { customer_id: customerId }
    });

    const monthsSinceFirstOrder = Math.max(1, 
      (new Date() - new Date(firstOrder.order_date)) / (1000 * 60 * 60 * 24 * 30)
    );

    return totalOrders / monthsSinceFirstOrder;
  } catch (error) {
    console.error('Error calculating order frequency:', error);
    return 0;
  }
};

/**
 * Calculate customer score based on multiple factors
 */
const calculateCustomerScore = async (customerId) => {
  try {
    const [lifetimeValue, avgOrderValue, orderFrequency] = await Promise.all([
      calculateCustomerLifetimeValue(customerId),
      calculateAverageOrderValue(customerId),
      calculateOrderFrequency(customerId)
    ]);

    // Scoring algorithm (0-100 scale)
    const lifetimeScore = Math.min(40, lifetimeValue / 1000); // Max 40 points for $40k+ LTV
    const orderValueScore = Math.min(30, avgOrderValue / 100); // Max 30 points for $3k+ avg order
    const frequencyScore = Math.min(30, orderFrequency * 10); // Max 30 points for 3+ orders/month

    return Math.round(lifetimeScore + orderValueScore + frequencyScore);
  } catch (error) {
    console.error('Error calculating customer score:', error);
    return 0;
  }
};

/**
 * Determine appropriate pricing tier based on purchase volume
 */
const calculatePricingTier = async (customerId) => {
  try {
    const lifetimeValue = await calculateCustomerLifetimeValue(customerId);
    const orderFrequency = await calculateOrderFrequency(customerId);

    // Tier calculation based on LTV and frequency
    if (lifetimeValue >= 50000 && orderFrequency >= 2) {
      return 'platinum';
    } else if (lifetimeValue >= 25000 && orderFrequency >= 1) {
      return 'gold';
    } else if (lifetimeValue >= 10000 || orderFrequency >= 0.5) {
      return 'silver';
    } else {
      return 'bronze';
    }
  } catch (error) {
    console.error('Error calculating pricing tier:', error);
    return 'bronze';
  }
};

/**
 * Get customer purchase trends and analytics
 */
const getCustomerAnalytics = async (customerId, months = 12) => {
  try {
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - months);

    const orders = await SalesOrder.findAll({
      where: {
        customer_id: customerId,
        order_date: { [Op.gte]: startDate }
      },
      include: [{
        model: OrderItem,
        as: 'orderItems',
        attributes: ['quantity', 'unit_price']
      }],
      order: [['order_date', 'ASC']]
    });

    // Monthly breakdown
    const monthlyData = {};
    orders.forEach(order => {
      const monthKey = order.order_date.toISOString().substring(0, 7); // YYYY-MM
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = {
          orders: 0,
          revenue: 0,
          items: 0
        };
      }
      monthlyData[monthKey].orders += 1;
      monthlyData[monthKey].revenue += parseFloat(order.total_amount);
      monthlyData[monthKey].items += order.orderItems.reduce((sum, item) => sum + item.quantity, 0);
    });

    const [lifetimeValue, avgOrderValue, orderFrequency, customerScore] = await Promise.all([
      calculateCustomerLifetimeValue(customerId),
      calculateAverageOrderValue(customerId),
      calculateOrderFrequency(customerId),
      calculateCustomerScore(customerId)
    ]);

    return {
      summary: {
        lifetimeValue,
        avgOrderValue,
        orderFrequency,
        customerScore,
        totalOrders: orders.length,
        totalItems: orders.reduce((sum, order) => 
          sum + order.orderItems.reduce((itemSum, item) => itemSum + item.quantity, 0), 0
        )
      },
      monthlyTrends: monthlyData,
      recentOrders: orders.slice(-5).map(order => ({
        id: order.id,
        date: order.order_date,
        amount: order.total_amount,
        status: order.status,
        itemCount: order.orderItems.reduce((sum, item) => sum + item.quantity, 0)
      }))
    };
  } catch (error) {
    console.error('Error getting customer analytics:', error);
    throw error;
  }
};

/**
 * Check if customer needs pricing tier upgrade
 */
const checkPricingTierUpgrade = async (customerId) => {
  try {
    const customer = await Customer.findByPk(customerId);
    if (!customer) return null;

    const recommendedTier = await calculatePricingTier(customerId);
    
    const tierHierarchy = ['bronze', 'silver', 'gold', 'platinum'];
    const currentIndex = tierHierarchy.indexOf(customer.pricing_tier);
    const recommendedIndex = tierHierarchy.indexOf(recommendedTier);

    if (recommendedIndex > currentIndex) {
      return {
        shouldUpgrade: true,
        currentTier: customer.pricing_tier,
        recommendedTier,
        benefits: getTierBenefits(recommendedTier)
      };
    }

    return { shouldUpgrade: false };
  } catch (error) {
    console.error('Error checking pricing tier upgrade:', error);
    return null;
  }
};

/**
 * Get benefits for a pricing tier
 */
const getTierBenefits = (tier) => {
  const benefits = {
    bronze: {
      discount: '0%',
      creditLimit: '$5,000',
      paymentTerms: '30 days',
      support: 'Standard'
    },
    silver: {
      discount: '5%',
      creditLimit: '$15,000',
      paymentTerms: '45 days',
      support: 'Priority'
    },
    gold: {
      discount: '10%',
      creditLimit: '$35,000',
      paymentTerms: '60 days',
      support: 'Premium'
    },
    platinum: {
      discount: '15%',
      creditLimit: '$75,000',
      paymentTerms: '90 days',
      support: 'Dedicated Account Manager'
    }
  };
  return benefits[tier] || benefits.bronze;
};

/**
 * Validate customer data for bulk import
 */
const validateCustomerImportData = (customerData) => {
  const errors = [];
  const requiredFields = ['company_name', 'contact_person', 'email'];

  requiredFields.forEach(field => {
    if (!customerData[field] || customerData[field].trim() === '') {
      errors.push(`${field} is required`);
    }
  });

  // Email validation
  if (customerData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(customerData.email)) {
    errors.push('Invalid email format');
  }

  // Phone validation
  if (customerData.phone && !/^[\+]?[1-9][\d]{0,15}$/.test(customerData.phone)) {
    errors.push('Invalid phone number format');
  }

  // Credit limit validation
  if (customerData.credit_limit && (isNaN(customerData.credit_limit) || customerData.credit_limit < 0)) {
    errors.push('Credit limit must be a non-negative number');
  }

  // Pricing tier validation
  const validTiers = ['bronze', 'silver', 'gold', 'platinum'];
  if (customerData.pricing_tier && !validTiers.includes(customerData.pricing_tier)) {
    errors.push('Invalid pricing tier');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

module.exports = {
  calculateCustomerLifetimeValue,
  calculateAverageOrderValue,
  calculateOrderFrequency,
  calculateCustomerScore,
  calculatePricingTier,
  getCustomerAnalytics,
  checkPricingTierUpgrade,
  getTierBenefits,
  validateCustomerImportData
};
