'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('customers', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      company_name: {
        type: Sequelize.STRING(200),
        allowNull: false
      },
      contact_info: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {}
      },
      credit_limit: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: false,
        defaultValue: 0.00
      },
      pricing_tier: {
        type: Sequelize.ENUM('standard', 'premium', 'wholesale'),
        allowNull: false,
        defaultValue: 'standard'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add indexes
    await queryInterface.addIndex('customers', ['company_name'], {
      name: 'customers_company_name_idx'
    });

    await queryInterface.addIndex('customers', ['pricing_tier'], {
      name: 'customers_pricing_tier_idx'
    });

    await queryInterface.addIndex('customers', ['credit_limit'], {
      name: 'customers_credit_limit_idx'
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('customers');
  }
};
