'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add indexes for reporting performance optimization
    
    // Inventory Items - for stock level and aging reports
    await queryInterface.addIndex('inventory_items', ['status', 'created_at'], {
      name: 'inventory_items_status_created_idx'
    });
    
    await queryInterface.addIndex('inventory_items', ['product_id', 'status'], {
      name: 'inventory_items_product_status_idx'
    });
    
    await queryInterface.addIndex('inventory_items', ['container_id', 'status'], {
      name: 'inventory_items_container_status_idx'
    });
    
    await queryInterface.addIndex('inventory_items', ['cost_price'], {
      name: 'inventory_items_cost_price_idx'
    });

    // Sales Orders - for sales reporting and trends
    await queryInterface.addIndex('sales_orders', ['order_date', 'status'], {
      name: 'sales_orders_date_status_idx'
    });
    
    await queryInterface.addIndex('sales_orders', ['customer_id', 'order_date'], {
      name: 'sales_orders_customer_date_idx'
    });
    
    await queryInterface.addIndex('sales_orders', ['status', 'total_amount'], {
      name: 'sales_orders_status_amount_idx'
    });
    
    await queryInterface.addIndex('sales_orders', ['order_date', 'total_amount'], {
      name: 'sales_orders_date_amount_idx'
    });

    // Order Items - for product sales analysis
    await queryInterface.addIndex('order_items', ['inventory_item_id', 'unit_price'], {
      name: 'order_items_inventory_price_idx'
    });
    
    await queryInterface.addIndex('order_items', ['order_id', 'quantity'], {
      name: 'order_items_order_quantity_idx'
    });

    // Containers - for container ROI and efficiency reports
    await queryInterface.addIndex('containers', ['arrival_date', 'status'], {
      name: 'containers_arrival_status_idx'
    });
    
    await queryInterface.addIndex('containers', ['status', 'updated_at'], {
      name: 'containers_status_updated_idx'
    });

    // Customers - for customer performance analysis
    await queryInterface.addIndex('customers', ['pricing_tier', 'status'], {
      name: 'customers_tier_status_idx'
    });
    
    await queryInterface.addIndex('customers', ['credit_limit', 'credit_used'], {
      name: 'customers_credit_analysis_idx'
    });
    
    await queryInterface.addIndex('customers', ['status', 'created_at'], {
      name: 'customers_status_created_idx'
    });

    // Products - for product performance analysis
    await queryInterface.addIndex('products', ['brand', 'model'], {
      name: 'products_brand_model_idx'
    });
    
    await queryInterface.addIndex('products', ['base_price'], {
      name: 'products_base_price_idx'
    });

    // Composite indexes for complex reporting queries
    await queryInterface.addIndex('inventory_items', ['product_id', 'status', 'created_at'], {
      name: 'inventory_items_product_status_date_idx'
    });
    
    await queryInterface.addIndex('sales_orders', ['customer_id', 'status', 'order_date'], {
      name: 'sales_orders_customer_status_date_idx'
    });
    
    // Partial indexes for active records only (PostgreSQL specific)
    await queryInterface.addIndex('inventory_items', ['status', 'cost_price'], {
      name: 'inventory_items_active_cost_idx',
      where: {
        deleted_at: null,
        status: ['available', 'reserved']
      }
    });
    
    await queryInterface.addIndex('sales_orders', ['order_date', 'total_amount'], {
      name: 'sales_orders_completed_date_amount_idx',
      where: {
        deleted_at: null,
        status: ['completed', 'delivered']
      }
    });
    
    await queryInterface.addIndex('customers', ['pricing_tier'], {
      name: 'customers_active_tier_idx',
      where: {
        deleted_at: null,
        status: 'active'
      }
    });

    // GIN indexes for JSONB fields (PostgreSQL specific)
    await queryInterface.addIndex('products', ['specifications_json'], {
      name: 'products_specifications_gin_idx',
      using: 'gin'
    });
    
    await queryInterface.addIndex('customers', ['address'], {
      name: 'customers_address_gin_idx',
      using: 'gin'
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove all the indexes we created
    const indexesToDrop = [
      'inventory_items_status_created_idx',
      'inventory_items_product_status_idx',
      'inventory_items_container_status_idx',
      'inventory_items_cost_price_idx',
      'sales_orders_date_status_idx',
      'sales_orders_customer_date_idx',
      'sales_orders_status_amount_idx',
      'sales_orders_date_amount_idx',
      'order_items_inventory_price_idx',
      'order_items_order_quantity_idx',
      'containers_arrival_status_idx',
      'containers_status_updated_idx',
      'customers_tier_status_idx',
      'customers_credit_analysis_idx',
      'customers_status_created_idx',
      'products_brand_model_idx',
      'products_base_price_idx',
      'inventory_items_product_status_date_idx',
      'sales_orders_customer_status_date_idx',
      'inventory_items_active_cost_idx',
      'sales_orders_completed_date_amount_idx',
      'customers_active_tier_idx',
      'products_specifications_gin_idx',
      'customers_address_gin_idx'
    ];

    for (const indexName of indexesToDrop) {
      try {
        await queryInterface.removeIndex('inventory_items', indexName);
      } catch (error) {
        // Index might not exist or be on a different table
      }
      
      try {
        await queryInterface.removeIndex('sales_orders', indexName);
      } catch (error) {
        // Index might not exist or be on a different table
      }
      
      try {
        await queryInterface.removeIndex('order_items', indexName);
      } catch (error) {
        // Index might not exist or be on a different table
      }
      
      try {
        await queryInterface.removeIndex('containers', indexName);
      } catch (error) {
        // Index might not exist or be on a different table
      }
      
      try {
        await queryInterface.removeIndex('customers', indexName);
      } catch (error) {
        // Index might not exist or be on a different table
      }
      
      try {
        await queryInterface.removeIndex('products', indexName);
      } catch (error) {
        // Index might not exist or be on a different table
      }
    }
  }
};
