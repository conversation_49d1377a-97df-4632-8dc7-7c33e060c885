'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // First, update the customers table with new fields
    
    // Add new columns to customers table
    await queryInterface.addColumn('customers', 'contact_person', {
      type: Sequelize.STRING(100),
      allowNull: false,
      defaultValue: 'Unknown'
    });

    await queryInterface.addColumn('customers', 'email', {
      type: Sequelize.STRING(255),
      allowNull: false,
      unique: true,
      defaultValue: '<EMAIL>'
    });

    await queryInterface.addColumn('customers', 'phone', {
      type: Sequelize.STRING(20),
      allowNull: true
    });

    await queryInterface.addColumn('customers', 'address', {
      type: Sequelize.JSONB,
      allowNull: true,
      defaultValue: {}
    });

    await queryInterface.addColumn('customers', 'credit_used', {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.00
    });

    await queryInterface.addColumn('customers', 'status', {
      type: Sequelize.ENUM('active', 'inactive', 'suspended'),
      allowNull: false,
      defaultValue: 'active'
    });

    await queryInterface.addColumn('customers', 'payment_terms', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 30
    });

    await queryInterface.addColumn('customers', 'deleted_at', {
      type: Sequelize.DATE,
      allowNull: true
    });

    // Update pricing_tier enum to new values
    // First, update existing data to map old values to new ones
    await queryInterface.sequelize.query(`
      UPDATE customers SET pricing_tier = 'bronze' WHERE pricing_tier = 'standard';
    `);
    await queryInterface.sequelize.query(`
      UPDATE customers SET pricing_tier = 'silver' WHERE pricing_tier = 'premium';
    `);
    await queryInterface.sequelize.query(`
      UPDATE customers SET pricing_tier = 'gold' WHERE pricing_tier = 'wholesale';
    `);

    // Remove the default constraint temporarily
    await queryInterface.sequelize.query(`
      ALTER TABLE customers ALTER COLUMN pricing_tier DROP DEFAULT;
    `);

    // Rename old enum type
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_customers_pricing_tier" RENAME TO "enum_customers_pricing_tier_old";
    `);

    // Create new enum type
    await queryInterface.sequelize.query(`
      CREATE TYPE "enum_customers_pricing_tier" AS ENUM('bronze', 'silver', 'gold', 'platinum');
    `);

    // Update column to use new enum type
    await queryInterface.sequelize.query(`
      ALTER TABLE customers
      ALTER COLUMN pricing_tier TYPE "enum_customers_pricing_tier"
      USING pricing_tier::text::"enum_customers_pricing_tier";
    `);

    // Set new default value
    await queryInterface.sequelize.query(`
      ALTER TABLE customers ALTER COLUMN pricing_tier SET DEFAULT 'bronze';
    `);

    // Drop old enum type
    await queryInterface.sequelize.query(`
      DROP TYPE "enum_customers_pricing_tier_old";
    `);

    // Update default credit limit
    await queryInterface.changeColumn('customers', 'credit_limit', {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 5000.00
    });

    // Remove old contact_info column (replaced by structured fields)
    await queryInterface.removeColumn('customers', 'contact_info');

    // Add new indexes for customers
    await queryInterface.addIndex('customers', ['email'], {
      name: 'customers_email_idx',
      unique: true
    });

    await queryInterface.addIndex('customers', ['status'], {
      name: 'customers_status_idx'
    });

    await queryInterface.addIndex('customers', ['contact_person'], {
      name: 'customers_contact_person_idx'
    });

    // Create full-text search index (requires gin extension)
    await queryInterface.sequelize.query(`
      CREATE EXTENSION IF NOT EXISTS pg_trgm;
    `);

    await queryInterface.sequelize.query(`
      CREATE INDEX customers_search_idx ON customers 
      USING gin((company_name || ' ' || contact_person) gin_trgm_ops);
    `);

    // Create customer_communications table
    await queryInterface.createTable('customer_communications', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      customer_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'customers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      type: {
        type: Sequelize.ENUM('email', 'phone', 'meeting', 'note', 'follow_up', 'complaint', 'support'),
        allowNull: false
      },
      subject: {
        type: Sequelize.STRING(200),
        allowNull: false
      },
      content: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      direction: {
        type: Sequelize.ENUM('inbound', 'outbound'),
        allowNull: false,
        defaultValue: 'outbound'
      },
      priority: {
        type: Sequelize.ENUM('low', 'medium', 'high', 'urgent'),
        allowNull: false,
        defaultValue: 'medium'
      },
      status: {
        type: Sequelize.ENUM('open', 'in_progress', 'resolved', 'closed'),
        allowNull: false,
        defaultValue: 'open'
      },
      follow_up_required: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      follow_up_date: {
        type: Sequelize.DATE,
        allowNull: true
      },
      communication_date: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      created_by: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {}
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      deleted_at: {
        type: Sequelize.DATE,
        allowNull: true
      }
    });

    // Add indexes for customer_communications
    await queryInterface.addIndex('customer_communications', ['customer_id'], {
      name: 'customer_communications_customer_id_idx'
    });

    await queryInterface.addIndex('customer_communications', ['type'], {
      name: 'customer_communications_type_idx'
    });

    await queryInterface.addIndex('customer_communications', ['status'], {
      name: 'customer_communications_status_idx'
    });

    await queryInterface.addIndex('customer_communications', ['priority'], {
      name: 'customer_communications_priority_idx'
    });

    await queryInterface.addIndex('customer_communications', ['communication_date'], {
      name: 'customer_communications_date_idx'
    });

    await queryInterface.addIndex('customer_communications', ['follow_up_date'], {
      name: 'customer_communications_follow_up_idx'
    });

    await queryInterface.addIndex('customer_communications', ['created_by'], {
      name: 'customer_communications_created_by_idx'
    });

    await queryInterface.addIndex('customer_communications', ['customer_id', 'communication_date'], {
      name: 'customer_communications_customer_date_idx'
    });

    await queryInterface.addIndex('customer_communications', ['follow_up_required', 'follow_up_date'], {
      name: 'customer_communications_follow_up_required_idx'
    });
  },

  async down (queryInterface, Sequelize) {
    // Drop customer_communications table
    await queryInterface.dropTable('customer_communications');

    // Remove new columns from customers table
    await queryInterface.removeColumn('customers', 'contact_person');
    await queryInterface.removeColumn('customers', 'email');
    await queryInterface.removeColumn('customers', 'phone');
    await queryInterface.removeColumn('customers', 'address');
    await queryInterface.removeColumn('customers', 'credit_used');
    await queryInterface.removeColumn('customers', 'status');
    await queryInterface.removeColumn('customers', 'payment_terms');
    await queryInterface.removeColumn('customers', 'deleted_at');

    // Restore old contact_info column
    await queryInterface.addColumn('customers', 'contact_info', {
      type: Sequelize.JSONB,
      allowNull: true,
      defaultValue: {}
    });

    // Restore old pricing_tier enum
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_customers_pricing_tier" RENAME TO "enum_customers_pricing_tier_old";
    `);

    await queryInterface.sequelize.query(`
      CREATE TYPE "enum_customers_pricing_tier" AS ENUM('standard', 'premium', 'wholesale');
    `);

    await queryInterface.sequelize.query(`
      ALTER TABLE customers 
      ALTER COLUMN pricing_tier TYPE "enum_customers_pricing_tier" 
      USING 'standard'::"enum_customers_pricing_tier";
    `);

    await queryInterface.sequelize.query(`
      DROP TYPE "enum_customers_pricing_tier_old";
    `);

    // Restore old credit limit default
    await queryInterface.changeColumn('customers', 'credit_limit', {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.00
    });

    // Drop search index
    await queryInterface.sequelize.query(`
      DROP INDEX IF EXISTS customers_search_idx;
    `);
  }
};
