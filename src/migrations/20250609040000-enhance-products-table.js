'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add new columns to products table
    await queryInterface.addColumn('products', 'image_url', {
      type: Sequelize.STRING,
      allowNull: true,
      validate: {
        isUrl: true
      }
    });

    await queryInterface.addColumn('products', 'price_history', {
      type: Sequelize.JSONB,
      allowNull: true,
      defaultValue: []
    });

    await queryInterface.addColumn('products', 'created_by', {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });

    await queryInterface.addColumn('products', 'updated_by', {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });

    await queryInterface.addColumn('products', 'deleted_at', {
      type: Sequelize.DATE,
      allowNull: true
    });

    // Add indexes for better performance
    await queryInterface.addIndex('products', ['created_by'], {
      name: 'products_created_by_idx'
    });

    await queryInterface.addIndex('products', ['updated_by'], {
      name: 'products_updated_by_idx'
    });

    await queryInterface.addIndex('products', ['deleted_at'], {
      name: 'products_deleted_at_idx'
    });

    // Add GIN index for full-text search (requires pg_trgm extension)
    try {
      await queryInterface.sequelize.query('CREATE EXTENSION IF NOT EXISTS pg_trgm;');
      await queryInterface.addIndex('products', ['brand', 'model'], {
        name: 'products_search_idx',
        using: 'gin',
        operator: 'gin_trgm_ops'
      });
    } catch (error) {
      console.warn('Could not create trigram index, pg_trgm extension may not be available:', error.message);
    }

    // Add GIN index for JSONB specifications
    await queryInterface.addIndex('products', ['specifications_json'], {
      name: 'products_specifications_idx',
      using: 'gin'
    });

    // Initialize price_history for existing products
    await queryInterface.sequelize.query(`
      UPDATE products 
      SET price_history = jsonb_build_array(
        jsonb_build_object(
          'price', base_price,
          'date', created_at,
          'updated_by', NULL,
          'reason', 'Initial price'
        )
      )
      WHERE price_history IS NULL OR price_history = '[]'::jsonb;
    `);
  },

  async down(queryInterface, Sequelize) {
    // Remove indexes
    await queryInterface.removeIndex('products', 'products_created_by_idx');
    await queryInterface.removeIndex('products', 'products_updated_by_idx');
    await queryInterface.removeIndex('products', 'products_deleted_at_idx');
    
    try {
      await queryInterface.removeIndex('products', 'products_search_idx');
    } catch (error) {
      console.warn('Could not remove trigram index:', error.message);
    }
    
    await queryInterface.removeIndex('products', 'products_specifications_idx');

    // Remove columns
    await queryInterface.removeColumn('products', 'deleted_at');
    await queryInterface.removeColumn('products', 'updated_by');
    await queryInterface.removeColumn('products', 'created_by');
    await queryInterface.removeColumn('products', 'price_history');
    await queryInterface.removeColumn('products', 'image_url');
  }
};
