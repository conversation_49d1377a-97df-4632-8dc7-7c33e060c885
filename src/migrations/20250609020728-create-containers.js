'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('containers', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      container_number: {
        type: Sequelize.STRING(50),
        allowNull: false,
        unique: true
      },
      arrival_date: {
        type: Sequelize.DATEONLY,
        allowNull: false
      },
      status: {
        type: Sequelize.ENUM('pending', 'arrived', 'processed'),
        allowNull: false,
        defaultValue: 'pending'
      },
      total_items: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add indexes
    await queryInterface.addIndex('containers', ['container_number'], {
      unique: true,
      name: 'containers_container_number_unique'
    });

    await queryInterface.addIndex('containers', ['status'], {
      name: 'containers_status_idx'
    });

    await queryInterface.addIndex('containers', ['arrival_date'], {
      name: 'containers_arrival_date_idx'
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('containers');
  }
};
