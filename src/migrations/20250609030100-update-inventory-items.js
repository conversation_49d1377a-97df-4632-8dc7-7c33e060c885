'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Add new condition value 'defective'
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_inventory_items_condition" ADD VALUE 'defective';
    `);

    // Add new status value 'returned'
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_inventory_items_status" ADD VALUE 'returned';
    `);

    // Add new columns
    await queryInterface.addColumn('inventory_items', 'location', {
      type: Sequelize.STRING(100),
      allowNull: true
    });

    await queryInterface.addColumn('inventory_items', 'warehouse', {
      type: Sequelize.STRING(50),
      allowNull: true,
      defaultValue: 'main'
    });

    await queryInterface.addColumn('inventory_items', 'purchase_date', {
      type: Sequelize.DATEONLY,
      allowNull: true
    });

    await queryInterface.addColumn('inventory_items', 'notes', {
      type: Sequelize.TEXT,
      allowNull: true
    });

    await queryInterface.addColumn('inventory_items', 'deleted_at', {
      type: Sequelize.DATE,
      allowNull: true
    });

    // Add indexes for new columns
    await queryInterface.addIndex('inventory_items', ['warehouse'], {
      name: 'inventory_items_warehouse_idx'
    });

    await queryInterface.addIndex('inventory_items', ['location'], {
      name: 'inventory_items_location_idx'
    });

    await queryInterface.addIndex('inventory_items', ['purchase_date'], {
      name: 'inventory_items_purchase_date_idx'
    });

    await queryInterface.addIndex('inventory_items', ['deleted_at'], {
      name: 'inventory_items_deleted_at_idx'
    });
  },

  async down (queryInterface, Sequelize) {
    // Remove indexes
    await queryInterface.removeIndex('inventory_items', 'inventory_items_warehouse_idx');
    await queryInterface.removeIndex('inventory_items', 'inventory_items_location_idx');
    await queryInterface.removeIndex('inventory_items', 'inventory_items_purchase_date_idx');
    await queryInterface.removeIndex('inventory_items', 'inventory_items_deleted_at_idx');

    // Remove columns
    await queryInterface.removeColumn('inventory_items', 'location');
    await queryInterface.removeColumn('inventory_items', 'warehouse');
    await queryInterface.removeColumn('inventory_items', 'purchase_date');
    await queryInterface.removeColumn('inventory_items', 'notes');
    await queryInterface.removeColumn('inventory_items', 'deleted_at');

    // Note: Removing enum values is complex and risky in production
    // In a real scenario, you might want to keep the enum values for data integrity
  }
};
