'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Check if containers table exists
    const tableExists = await queryInterface.sequelize.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'containers'
      );
    `);

    if (tableExists[0][0].exists) {
      // Check if status column exists
      const statusColumnExists = await queryInterface.sequelize.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.columns
          WHERE table_schema = 'public'
          AND table_name = 'containers'
          AND column_name = 'status'
        );
      `);

      if (statusColumnExists[0][0].exists) {
        // First, remove the default value
        await queryInterface.sequelize.query(`
          ALTER TABLE containers ALTER COLUMN status DROP DEFAULT;
        `);

        // Clean up any existing old enum type
        await queryInterface.sequelize.query(`
          DROP TYPE IF EXISTS "enum_containers_status_old" CASCADE;
        `);

        // Update status enum values
        await queryInterface.sequelize.query(`
          ALTER TYPE "enum_containers_status" RENAME TO "enum_containers_status_old";
        `);

        await queryInterface.sequelize.query(`
          CREATE TYPE "enum_containers_status" AS ENUM('arrived', 'processing', 'completed');
        `);

        // Update existing data to map old values to new values
        await queryInterface.sequelize.query(`
          UPDATE containers
          SET status = CASE
            WHEN status = 'pending' THEN 'arrived'
            WHEN status = 'processed' THEN 'completed'
            ELSE status
          END;
        `);

        await queryInterface.sequelize.query(`
          ALTER TABLE containers
          ALTER COLUMN status TYPE "enum_containers_status"
          USING status::text::"enum_containers_status";
        `);

        // Set new default value
        await queryInterface.sequelize.query(`
          ALTER TABLE containers ALTER COLUMN status SET DEFAULT 'arrived';
        `);

        await queryInterface.sequelize.query(`
          DROP TYPE "enum_containers_status_old";
        `);
      }
    }

    // Add new audit fields (check if they don't already exist)
    const deletedAtExists = await queryInterface.sequelize.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'containers'
        AND column_name = 'deleted_at'
      );
    `);

    if (!deletedAtExists[0][0].exists) {
      await queryInterface.addColumn('containers', 'deleted_at', {
        type: Sequelize.DATE,
        allowNull: true,
        defaultValue: null
      });
    }

    const createdByExists = await queryInterface.sequelize.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'containers'
        AND column_name = 'created_by'
      );
    `);

    if (!createdByExists[0][0].exists) {
      await queryInterface.addColumn('containers', 'created_by', {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      });
    }

    const updatedByExists = await queryInterface.sequelize.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'containers'
        AND column_name = 'updated_by'
      );
    `);

    if (!updatedByExists[0][0].exists) {
      await queryInterface.addColumn('containers', 'updated_by', {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      });
    }

    // Add index for deleted_at (check if it doesn't already exist)
    try {
      await queryInterface.addIndex('containers', ['deleted_at']);
    } catch (error) {
      // Index might already exist, ignore error
      console.log('Index for deleted_at already exists or failed to create');
    }

    // Update unique constraint to exclude soft-deleted records
    try {
      await queryInterface.removeIndex('containers', ['container_number']);
    } catch (error) {
      // Index might not exist, ignore error
      console.log('Container number index does not exist or failed to remove');
    }

    try {
      await queryInterface.addIndex('containers', ['container_number'], {
        unique: true,
        where: {
          deleted_at: null
        }
      });
    } catch (error) {
      // Index might already exist, ignore error
      console.log('Unique container number index already exists or failed to create');
    }

    // Update default status for existing records (only if containers table exists and has data)
    const containerCount = await queryInterface.sequelize.query(`
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = 'public' AND table_name = 'containers';
    `);

    if (containerCount[0][0].count > 0) {
      const hasStatusColumn = await queryInterface.sequelize.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.columns
          WHERE table_schema = 'public'
          AND table_name = 'containers'
          AND column_name = 'status'
        );
      `);

      if (hasStatusColumn[0][0].exists) {
        await queryInterface.sequelize.query(`
          UPDATE containers
          SET status = 'arrived'
          WHERE status IS NULL;
        `);
      }
    }
  },

  async down (queryInterface, Sequelize) {
    // Remove audit fields
    await queryInterface.removeColumn('containers', 'deleted_at');
    await queryInterface.removeColumn('containers', 'created_by');
    await queryInterface.removeColumn('containers', 'updated_by');

    // Remove current default
    await queryInterface.sequelize.query(`
      ALTER TABLE containers ALTER COLUMN status DROP DEFAULT;
    `);

    // Revert status enum
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_containers_status" RENAME TO "enum_containers_status_old";
    `);

    await queryInterface.sequelize.query(`
      CREATE TYPE "enum_containers_status" AS ENUM('pending', 'arrived', 'processed');
    `);

    // Update data to map back to old values
    await queryInterface.sequelize.query(`
      UPDATE containers
      SET status = CASE
        WHEN status = 'processing' THEN 'arrived'
        WHEN status = 'completed' THEN 'processed'
        ELSE status
      END;
    `);

    await queryInterface.sequelize.query(`
      ALTER TABLE containers
      ALTER COLUMN status TYPE "enum_containers_status"
      USING status::text::"enum_containers_status";
    `);

    // Set old default value
    await queryInterface.sequelize.query(`
      ALTER TABLE containers ALTER COLUMN status SET DEFAULT 'pending';
    `);

    await queryInterface.sequelize.query(`
      DROP TYPE "enum_containers_status_old";
    `);

    // Restore original unique constraint
    await queryInterface.removeIndex('containers', ['container_number']);
    await queryInterface.addIndex('containers', ['container_number'], {
      unique: true
    });
  }
};
