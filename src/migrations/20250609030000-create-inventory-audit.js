'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('inventory_audit', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      inventory_item_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'inventory_items',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      field_name: {
        type: Sequelize.STRING(50),
        allowNull: false
      },
      old_value: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      new_value: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      changed_by: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      change_reason: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      ip_address: {
        type: Sequelize.INET,
        allowNull: true
      },
      user_agent: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add indexes
    await queryInterface.addIndex('inventory_audit', ['inventory_item_id'], {
      name: 'inventory_audit_inventory_item_id_idx'
    });

    await queryInterface.addIndex('inventory_audit', ['field_name'], {
      name: 'inventory_audit_field_name_idx'
    });

    await queryInterface.addIndex('inventory_audit', ['changed_by'], {
      name: 'inventory_audit_changed_by_idx'
    });

    await queryInterface.addIndex('inventory_audit', ['created_at'], {
      name: 'inventory_audit_created_at_idx'
    });

    await queryInterface.addIndex('inventory_audit', ['inventory_item_id', 'field_name'], {
      name: 'inventory_audit_item_field_idx'
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('inventory_audit');
  }
};
