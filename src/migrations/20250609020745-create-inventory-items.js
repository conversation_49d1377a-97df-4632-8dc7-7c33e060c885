'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('inventory_items', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      product_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'products',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      },
      container_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'containers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      serial_number: {
        type: Sequelize.STRING(100),
        allowNull: false,
        unique: true
      },
      condition: {
        type: Sequelize.ENUM('new', 'refurbished', 'damaged'),
        allowNull: false,
        defaultValue: 'new'
      },
      status: {
        type: Sequelize.ENUM('available', 'reserved', 'sold'),
        allowNull: false,
        defaultValue: 'available'
      },
      cost_price: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add indexes
    await queryInterface.addIndex('inventory_items', ['serial_number'], {
      unique: true,
      name: 'inventory_items_serial_number_unique'
    });

    await queryInterface.addIndex('inventory_items', ['product_id'], {
      name: 'inventory_items_product_id_idx'
    });

    await queryInterface.addIndex('inventory_items', ['container_id'], {
      name: 'inventory_items_container_id_idx'
    });

    await queryInterface.addIndex('inventory_items', ['status'], {
      name: 'inventory_items_status_idx'
    });

    await queryInterface.addIndex('inventory_items', ['condition'], {
      name: 'inventory_items_condition_idx'
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('inventory_items');
  }
};
