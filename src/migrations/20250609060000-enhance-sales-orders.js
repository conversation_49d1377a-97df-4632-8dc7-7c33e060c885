'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // First, drop the existing enum type and recreate with new values
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_sales_orders_status" 
      ADD VALUE IF NOT EXISTS 'processing';
    `);
    
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_sales_orders_status" 
      ADD VALUE IF NOT EXISTS 'completed';
    `);

    // Add soft delete support
    await queryInterface.addColumn('sales_orders', 'deleted_at', {
      type: Sequelize.DATE,
      allowNull: true,
      defaultValue: null
    });

    // Add notes field for order comments
    await queryInterface.addColumn('sales_orders', 'notes', {
      type: Sequelize.TEXT,
      allowNull: true
    });

    // Add shipping_info field for fulfillment details
    await queryInterface.addColumn('sales_orders', 'shipping_info', {
      type: Sequelize.JSONB,
      allowNull: true
    });

    // Add index for deleted_at
    await queryInterface.addIndex('sales_orders', ['deleted_at'], {
      name: 'sales_orders_deleted_at_idx'
    });

    // Add composite index for active orders
    await queryInterface.addIndex('sales_orders', ['status', 'deleted_at'], {
      name: 'sales_orders_status_active_idx',
      where: {
        deleted_at: null
      }
    });

    // Add index for customer orders
    await queryInterface.addIndex('sales_orders', ['customer_id', 'deleted_at'], {
      name: 'sales_orders_customer_active_idx',
      where: {
        deleted_at: null
      }
    });
  },

  async down (queryInterface, Sequelize) {
    // Remove indexes
    await queryInterface.removeIndex('sales_orders', 'sales_orders_deleted_at_idx');
    await queryInterface.removeIndex('sales_orders', 'sales_orders_status_active_idx');
    await queryInterface.removeIndex('sales_orders', 'sales_orders_customer_active_idx');

    // Remove columns
    await queryInterface.removeColumn('sales_orders', 'deleted_at');
    await queryInterface.removeColumn('sales_orders', 'notes');
    await queryInterface.removeColumn('sales_orders', 'shipping_info');

    // Note: We don't remove the enum values in down migration to avoid data loss
    // The enum values 'processing' and 'completed' will remain available
  }
};
