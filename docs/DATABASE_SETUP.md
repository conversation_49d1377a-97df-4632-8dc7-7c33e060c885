# Database Setup and Configuration Guide

## Overview

This document provides comprehensive instructions for setting up and configuring PostgreSQL with Sequelize ORM for the Bulk Laptop Inventory Management System.

## Prerequisites

- PostgreSQL 12+ installed and running
- Node.js 14+ with npm
- Access to PostgreSQL command line tools (`psql`, `createdb`)

## Quick Setup

### 1. Database Creation

```bash
# Create development database
createdb inventory_manager_dev

# Create test database
createdb inventory_manager_test
```

### 2. Environment Configuration

Copy the `.env.example` file to `.env` and configure your database settings:

```bash
cp .env.example .env
```

Update the following variables in your `.env` file:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=inventory_manager_dev
DB_USER=your_username
DB_PASSWORD=your_password

# Test Database
TEST_DB_NAME=inventory_manager_test
```

### 3. Test Database Connection

```bash
npm run db:test
```

### 4. Run Migrations

```bash
npm run db:migrate
```

## Database Configuration

### Environment-Specific Settings

The system supports three environments:

- **Development**: Full logging, local database
- **Test**: No logging, separate test database
- **Production**: Connection pooling, SSL support, optimized settings

### Configuration Files

- **`.sequelizerc`**: Sequelize CLI configuration
- **`src/config/database.js`**: Database connection and environment settings
- **`.env`**: Environment variables (not committed to git)
- **`.env.example`**: Template for environment variables

## Available Scripts

| Script | Command | Description |
|--------|---------|-------------|
| `npm run db:create` | `npx sequelize-cli db:create` | Create database |
| `npm run db:drop` | `npx sequelize-cli db:drop` | Drop database |
| `npm run db:migrate` | `npx sequelize-cli db:migrate` | Run migrations |
| `npm run db:migrate:undo` | `npx sequelize-cli db:migrate:undo` | Undo last migration |
| `npm run db:seed` | `npx sequelize-cli db:seed:all` | Run all seeders |
| `npm run db:seed:undo` | `npx sequelize-cli db:seed:undo:all` | Undo all seeders |
| `npm run db:test` | `node scripts/test-db-connection.js` | Test database connection |

## Health Checks

The application includes database health monitoring:

### Health Endpoints

- **`GET /health`**: Server and database health status
- **`GET /api/health`**: API-specific health with database status

### Health Response Example

```json
{
  "status": "OK",
  "timestamp": "2025-06-09T01:42:30.362Z",
  "database": {
    "status": "connected",
    "connection_time": "31ms",
    "database_name": "inventory_manager_dev",
    "host": "localhost",
    "port": 5432,
    "dialect": "postgres",
    "version": "PostgreSQL 14.17...",
    "last_check": "2025-06-09T01:42:30.362Z"
  }
}
```

## Testing

### Database Tests

Run database-specific tests:

```bash
npm test tests/database.test.js
```

### Test Coverage

The database tests cover:
- Connection establishment
- Query execution
- Health status reporting
- Configuration validation
- Error handling

## Troubleshooting

### Common Issues

1. **Connection Refused**
   ```bash
   # Check if PostgreSQL is running
   brew services list | grep postgresql
   
   # Start PostgreSQL if not running
   brew services start postgresql@14
   ```

2. **Database Does Not Exist**
   ```bash
   # Create the database
   createdb inventory_manager_dev
   ```

3. **Permission Denied**
   ```bash
   # Check database permissions
   psql -l
   
   # Connect as superuser if needed
   psql -U postgres
   ```

4. **Migration Errors**
   ```bash
   # Check migration status
   npx sequelize-cli db:migrate:status
   
   # Undo problematic migration
   npm run db:migrate:undo
   ```

### Debug Mode

Enable detailed logging by setting:

```env
NODE_ENV=development
```

This will show all SQL queries in the console.

## Production Considerations

### SSL Configuration

For production deployments, enable SSL:

```env
DB_SSL=true
DB_SSL_REJECT_UNAUTHORIZED=true
```

### Connection Pooling

Configure connection pool settings:

```env
DB_POOL_MAX=10
DB_POOL_MIN=0
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000
```

### Security

- Use strong passwords
- Limit database user permissions
- Enable SSL/TLS encryption
- Use connection pooling
- Monitor connection health

## Next Steps

1. Create your first model using Sequelize CLI
2. Set up database seeders for initial data
3. Implement proper backup strategies
4. Configure monitoring and alerting
5. Set up database migrations for production deployment
