# Customer Management Module

## Overview

The Customer Management Module provides comprehensive retailer relationship management capabilities for the inventory management system. It includes credit management, pricing tiers, purchase analytics, communication logging, and performance tracking.

## Features

### 🏢 Customer Management
- **Customer Profiles**: Complete retailer information with contact details, addresses, and business information
- **Credit Management**: Real-time credit limit tracking, utilization monitoring, and automatic credit holds
- **Pricing Tiers**: Volume-based discount system (Bronze, Silver, Gold, Platinum)
- **Status Management**: Active, inactive, and suspended customer states
- **Soft Delete**: Paranoid deletion with audit trail preservation

### 💰 Credit & Financial Management
- **Credit Limits**: Configurable credit limits with real-time utilization tracking
- **Credit Utilization**: Automatic calculation of credit usage percentage
- **Available Credit**: Real-time available credit calculations
- **Payment Terms**: Configurable payment terms (0-365 days)
- **Credit Alerts**: Automatic alerts when credit limits are exceeded

### 🎯 Pricing Tier System
- **Bronze Tier**: 0% discount, $5,000 default credit limit
- **Silver Tier**: 5% discount, $15,000 credit limit
- **Gold Tier**: 10% discount, $35,000 credit limit
- **Platinum Tier**: 15% discount, $75,000 credit limit
- **Automatic Upgrades**: Based on purchase volume and frequency

### 📊 Analytics & Reporting
- **Customer Lifetime Value**: Total purchase value calculation
- **Average Order Value**: Purchase pattern analysis
- **Order Frequency**: Monthly order frequency tracking
- **Customer Scoring**: Performance-based scoring (0-100 scale)
- **Purchase Trends**: Monthly and quarterly trend analysis
- **Tier Upgrade Recommendations**: Automatic tier upgrade suggestions

### 💬 Communication Management
- **Communication Logging**: Track all customer interactions
- **Communication Types**: Email, phone, meeting, notes, follow-ups, complaints, support
- **Follow-up Management**: Automated follow-up reminders
- **Priority Levels**: Low, medium, high, urgent priority classification
- **Status Tracking**: Open, in-progress, resolved, closed status workflow

### 🔍 Search & Filtering
- **Full-text Search**: Search across company names and contact persons
- **Advanced Filtering**: Filter by pricing tier, status, credit limits
- **Pagination**: Efficient pagination for large datasets
- **Sorting**: Multiple sorting options

### 📥 Bulk Operations
- **Bulk Import**: CSV-based customer import with validation
- **Bulk Export**: Export customer data for reporting
- **Validation**: Comprehensive data validation during import
- **Error Reporting**: Detailed error reporting for failed imports

## API Endpoints

### Customer Management

#### Create Customer
```http
POST /api/customers
Authorization: Bearer <token>
Roles: admin, manager

{
  "company_name": "Tech Solutions Inc",
  "contact_person": "John Smith",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "address": {
    "street": "123 Business St",
    "city": "Tech City",
    "state": "CA",
    "postal_code": "12345",
    "country": "USA"
  },
  "credit_limit": 15000.00,
  "pricing_tier": "silver",
  "payment_terms": 45
}
```

#### List Customers
```http
GET /api/customers?page=1&limit=20&search=tech&pricing_tier=silver&status=active
Authorization: Bearer <token>
Roles: admin, manager, employee
```

#### Get Customer Details
```http
GET /api/customers/:id
Authorization: Bearer <token>
Roles: admin, manager, employee
```

#### Update Customer
```http
PUT /api/customers/:id
Authorization: Bearer <token>
Roles: admin, manager, employee (limited fields)

{
  "company_name": "Updated Tech Solutions Inc",
  "pricing_tier": "gold",
  "credit_limit": 25000.00
}
```

#### Get Customer Orders
```http
GET /api/customers/:id/orders?page=1&limit=20&status=delivered&date_from=2024-01-01
Authorization: Bearer <token>
Roles: admin, manager, employee
```

### Communication Management

#### Create Communication
```http
POST /api/customers/:id/communications
Authorization: Bearer <token>
Roles: admin, manager, employee

{
  "type": "email",
  "subject": "Follow-up on recent order",
  "content": "Following up on your recent laptop order...",
  "direction": "outbound",
  "priority": "medium",
  "follow_up_required": true,
  "follow_up_date": "2024-07-01T10:00:00Z"
}
```

### Bulk Operations

#### Bulk Import
```http
POST /api/customers/bulk-import
Authorization: Bearer <token>
Roles: admin, manager

{
  "customers": [
    {
      "company_name": "Retailer 1",
      "contact_person": "Jane Doe",
      "email": "<EMAIL>",
      "credit_limit": 10000.00,
      "pricing_tier": "bronze"
    }
  ]
}
```

## Database Schema

### Customers Table
```sql
CREATE TABLE customers (
  id SERIAL PRIMARY KEY,
  company_name VARCHAR(200) NOT NULL,
  contact_person VARCHAR(100) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  phone VARCHAR(20),
  address JSONB,
  credit_limit DECIMAL(12,2) NOT NULL DEFAULT 5000.00,
  credit_used DECIMAL(12,2) NOT NULL DEFAULT 0.00,
  pricing_tier ENUM('bronze','silver','gold','platinum') DEFAULT 'bronze',
  status ENUM('active','inactive','suspended') DEFAULT 'active',
  payment_terms INTEGER NOT NULL DEFAULT 30,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP WITH TIME ZONE
);
```

### Customer Communications Table
```sql
CREATE TABLE customer_communications (
  id SERIAL PRIMARY KEY,
  customer_id INTEGER REFERENCES customers(id),
  type VARCHAR(20) NOT NULL,
  subject VARCHAR(200) NOT NULL,
  content TEXT NOT NULL,
  direction VARCHAR(10) DEFAULT 'outbound',
  priority VARCHAR(10) DEFAULT 'medium',
  status VARCHAR(15) DEFAULT 'open',
  follow_up_required BOOLEAN DEFAULT false,
  follow_up_date TIMESTAMP WITH TIME ZONE,
  communication_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  created_by UUID REFERENCES users(id),
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP WITH TIME ZONE
);
```

## Security & Authorization

### Role-Based Access Control
- **Admin**: Full access to all customer operations including credit limit modifications
- **Manager**: Full access except sensitive financial operations
- **Employee**: Read access and basic customer operations (cannot modify credit limits)

### Rate Limiting
- **Customer Creation**: 10 requests per 15 minutes
- **Bulk Operations**: 5 requests per hour
- **General Operations**: 200 requests per 15 minutes

### Data Validation
- **Email Uniqueness**: Enforced at database and application level
- **Credit Limits**: Non-negative values with maximum limits
- **Phone Numbers**: E.164 format validation
- **Pricing Tiers**: Enum validation with automatic tier calculations

## Analytics Features

### Customer Scoring Algorithm
```javascript
// Scoring components (0-100 scale)
const lifetimeScore = Math.min(40, lifetimeValue / 1000);     // Max 40 points
const orderValueScore = Math.min(30, avgOrderValue / 100);    // Max 30 points  
const frequencyScore = Math.min(30, orderFrequency * 10);     // Max 30 points

const totalScore = lifetimeScore + orderValueScore + frequencyScore;
```

### Pricing Tier Calculation
```javascript
if (lifetimeValue >= 50000 && orderFrequency >= 2) return 'platinum';
if (lifetimeValue >= 25000 && orderFrequency >= 1) return 'gold';
if (lifetimeValue >= 10000 || orderFrequency >= 0.5) return 'silver';
return 'bronze';
```

## Testing

### Unit Tests
- Customer model validation
- Controller business logic
- Utility function calculations
- Authentication and authorization

### Integration Tests
- API endpoint functionality
- Database operations
- Role-based access control
- Rate limiting enforcement

### Test Coverage
- Customer CRUD operations
- Credit management workflows
- Pricing tier calculations
- Communication logging
- Bulk import/export operations

## Performance Optimizations

### Database Indexes
- Full-text search index on company_name and contact_person
- Composite indexes for common query patterns
- Foreign key indexes for join operations

### Caching Strategy
- Customer analytics caching for frequently accessed data
- Pricing tier calculation caching
- Search result caching for common queries

### Query Optimization
- Efficient pagination with offset/limit
- Selective field loading based on user roles
- Optimized joins for related data

## Monitoring & Logging

### Audit Logging
- All customer data changes logged to communications table
- User attribution for all modifications
- Timestamp tracking for compliance

### Security Logging
- Authentication attempts and failures
- Authorization violations
- Rate limit violations
- Sensitive data access

### Performance Monitoring
- API response times
- Database query performance
- Cache hit rates
- Error rates and patterns
