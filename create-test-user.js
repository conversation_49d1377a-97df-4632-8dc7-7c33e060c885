const { User } = require('./src/models');
const jwt = require('jsonwebtoken');

async function createTestUser() {
  try {
    // Check if admin user exists
    let user = await User.findOne({ where: { username: 'admin' } });
    
    if (!user) {
      console.log('Creating admin user...');
      user = await User.create({
        username: 'admin',
        email: '<EMAIL>',
        password_hash: 'hashedpassword',
        role: 'admin',
        is_active: true
      });
    }
    
    console.log('User found/created:', user.username, user.role);
    
    // Generate valid token
    const token = jwt.sign(
      { 
        id: user.id, 
        username: user.username, 
        role: user.role 
      },
      process.env.JWT_SECRET || 'your_super_secret_jwt_key_change_this_in_production',
      { 
        expiresIn: '1h',
        issuer: 'inventory-manager',
        audience: 'inventory-manager-users'
      }
    );
    
    console.log('Generated token for testing:');
    console.log(token);
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

createTestUser();
