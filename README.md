# Bulk Laptop Inventory Management System

A comprehensive inventory and sales management system for businesses that import laptops via shipping containers and sell them to retailers.

## Features

- **Inventory Management**: Track laptop inventory from container imports to individual sales
- **Sales Management**: Handle bulk sales to retailers and individual customers
- **User Authentication**: Secure JWT-based authentication system
- **RESTful API**: Well-structured API endpoints for all operations
- **Database Integration**: PostgreSQL with Sequelize ORM
- **Security**: Helmet.js security headers, rate limiting, and input validation
- **Testing**: Comprehensive test suite with Jest and Supertest

## Tech Stack

- **Backend**: Node.js with Express.js
- **Database**: PostgreSQL with Sequelize ORM
- **Authentication**: JWT (JSON Web Tokens)
- **Security**: Helmet.js, CORS, Rate Limiting
- **Testing**: Jest, Supertest
- **Development**: Nodemon for auto-restart

## Project Structure

```
inventory-manager/
├── src/
│   ├── controllers/     # Business logic handlers
│   ├── models/         # Database models (Sequelize)
│   ├── routes/         # API route definitions
│   ├── middleware/     # Custom middleware functions
│   ├── config/         # Configuration files
│   └── utils/          # Utility functions
├── tests/              # Test files
├── server.js           # Main application entry point
├── package.json        # Dependencies and scripts
├── .env.example        # Environment variables template
└── README.md          # Project documentation
```

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/HafizAbdullahUmar/inventory-manager.git
   cd inventory-manager
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your actual configuration values
   ```

4. Set up the database:
   ```bash
   # Create PostgreSQL databases
   createdb inventory_manager_dev
   createdb inventory_manager_test

   # Test database connection
   npm run db:test

   # Run initial migrations (when available)
   npm run db:migrate
   ```

   For detailed database setup instructions, see [Database Setup Guide](docs/DATABASE_SETUP.md).

### Running the Application

#### Development Mode
```bash
npm run dev
```

#### Production Mode
```bash
npm start
```

#### Running Tests
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Test database connection
npm run db:test
```

#### Database Management
```bash
# Create databases
npm run db:create

# Run migrations
npm run db:migrate

# Undo last migration
npm run db:migrate:undo

# Run seeders
npm run db:seed

# Test database connection
npm run db:test
```

## API Endpoints

### Health Checks
- `GET /health` - Server health status with database connectivity
- `GET /api/health` - API health status with database connectivity

Both endpoints return HTTP 200 when healthy, HTTP 503 when database is unavailable.

### Authentication (Coming Soon)
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/refresh` - Refresh JWT token

### Inventory Management (Coming Soon)
- `GET /api/inventory` - Get all inventory items
- `POST /api/inventory` - Add new inventory
- `PUT /api/inventory/:id` - Update inventory item
- `DELETE /api/inventory/:id` - Remove inventory item

## Environment Variables

Copy `.env.example` to `.env` and configure the following variables:

```env
# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=inventory_manager_dev
DB_USER=your_db_user
DB_PASSWORD=your_db_password

# Test Database Configuration
TEST_DB_NAME=inventory_manager_test

# Authentication
JWT_SECRET=your_super_secret_jwt_key
JWT_EXPIRES_IN=24h

# Security
BCRYPT_ROUNDS=12

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the ISC License - see the [LICENSE](LICENSE) file for details.

## Support

For support, email <EMAIL> or create an issue in the GitHub repository.
