const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

// Test credentials
const adminCredentials = {
  username: 'admin',
  password: 'Admin123!@#'
};

async function testAnalytics() {
  try {
    // Login
    console.log('🔐 Logging in as admin...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, adminCredentials);
    const authToken = loginResponse.data.token;
    console.log('✅ Login successful');

    // Test analytics
    console.log('📊 Fetching product analytics...');
    const response = await axios.get(`${BASE_URL}/products/analytics`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    console.log('✅ Analytics retrieved successfully!');
    console.log('\n📊 Analytics Results:');
    console.log('Overview:', JSON.stringify(response.data.analytics.overview, null, 2));
    console.log('Brand Distribution:', JSON.stringify(response.data.analytics.brand_distribution, null, 2));
    console.log('Price Distribution:', JSON.stringify(response.data.analytics.price_distribution, null, 2));
    console.log('Popular Specifications:', JSON.stringify(response.data.analytics.popular_specifications, null, 2));
    console.log('Top Performing Products:', JSON.stringify(response.data.analytics.top_performing_products, null, 2));

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testAnalytics();
