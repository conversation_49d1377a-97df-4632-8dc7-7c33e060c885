const { testDatabaseConnection, getDatabaseHealth, sequelize } = require('../src/utils/dbConnection');

describe('Database Connection Tests', () => {
  afterAll(async () => {
    // Close database connection after all tests
    await sequelize.close();
  });

  describe('testDatabaseConnection', () => {
    it('should successfully connect to the database', async () => {
      const result = await testDatabaseConnection();
      
      expect(result).toHaveProperty('status');
      expect(result).toHaveProperty('message');
      expect(result).toHaveProperty('connectionTime');
      expect(result).toHaveProperty('database');
      expect(result).toHaveProperty('host');
      expect(result).toHaveProperty('port');
      expect(result).toHaveProperty('dialect', 'postgres');
      expect(result).toHaveProperty('timestamp');
      
      if (result.status === 'connected') {
        expect(result.status).toBe('connected');
        expect(result.message).toBe('Database connection successful');
        expect(result).toHaveProperty('version');
        expect(result.connectionTime).toMatch(/^\d+ms$/);
      } else {
        expect(result.status).toBe('disconnected');
        expect(result.message).toBe('Database connection failed');
        expect(result).toHaveProperty('error');
      }
    }, 10000);

    it('should return connection time as a string with ms suffix', async () => {
      const result = await testDatabaseConnection();
      expect(result.connectionTime).toMatch(/^\d+ms$/);
    });

    it('should return a valid timestamp', async () => {
      const result = await testDatabaseConnection();
      expect(new Date(result.timestamp)).toBeInstanceOf(Date);
      expect(new Date(result.timestamp).getTime()).not.toBeNaN();
    });
  });

  describe('getDatabaseHealth', () => {
    it('should return database health status', async () => {
      const health = await getDatabaseHealth();
      
      expect(health).toHaveProperty('database');
      expect(health.database).toHaveProperty('status');
      expect(health.database).toHaveProperty('last_check');
      
      if (health.database.status === 'connected') {
        expect(health.database).toHaveProperty('connection_time');
        expect(health.database).toHaveProperty('database_name');
        expect(health.database).toHaveProperty('host');
        expect(health.database).toHaveProperty('port');
        expect(health.database).toHaveProperty('dialect', 'postgres');
        expect(health.database).toHaveProperty('version');
      } else if (health.database.status === 'error') {
        expect(health.database).toHaveProperty('error');
      }
    }, 10000);

    it('should return a valid timestamp for last_check', async () => {
      const health = await getDatabaseHealth();
      expect(new Date(health.database.last_check)).toBeInstanceOf(Date);
      expect(new Date(health.database.last_check).getTime()).not.toBeNaN();
    });
  });

  describe('Sequelize Instance', () => {
    it('should have correct database configuration', () => {
      expect(sequelize.getDatabaseName()).toBe('inventory_manager_test');
      expect(sequelize.getDialect()).toBe('postgres');
      expect(sequelize.config.host).toBe('localhost');
      expect(sequelize.config.port).toBe(5432);
    });

    it('should be able to authenticate', async () => {
      try {
        await sequelize.authenticate();
        // If we reach here, authentication was successful
        expect(true).toBe(true);
      } catch (error) {
        // If authentication fails, we should still handle it gracefully
        expect(error).toBeInstanceOf(Error);
        console.warn('Database authentication failed in test environment:', error.message);
      }
    }, 10000);
  });

  describe('Database Query Execution', () => {
    it('should be able to execute basic queries', async () => {
      try {
        const [results] = await sequelize.query('SELECT NOW() as current_time');
        expect(results).toHaveLength(1);
        expect(results[0]).toHaveProperty('current_time');
        expect(new Date(results[0].current_time)).toBeInstanceOf(Date);
      } catch (error) {
        // If query fails, we should handle it gracefully
        expect(error).toBeInstanceOf(Error);
        console.warn('Database query failed in test environment:', error.message);
      }
    }, 10000);

    it('should be able to get PostgreSQL version', async () => {
      try {
        const [results] = await sequelize.query('SELECT version() as pg_version');
        expect(results).toHaveLength(1);
        expect(results[0]).toHaveProperty('pg_version');
        expect(results[0].pg_version).toContain('PostgreSQL');
      } catch (error) {
        // If query fails, we should handle it gracefully
        expect(error).toBeInstanceOf(Error);
        console.warn('PostgreSQL version query failed in test environment:', error.message);
      }
    }, 10000);
  });
});
