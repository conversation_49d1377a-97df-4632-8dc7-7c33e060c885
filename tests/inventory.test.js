const request = require('supertest');
const app = require('../server');
const { User, Product, Container, InventoryItem, InventoryAudit } = require('../src/models');
const { sequelize } = require('../src/models');

describe('Inventory Management System', () => {
  let authToken;
  let adminUser;
  let testProduct;
  let testContainer;
  let testInventoryItem;

  beforeAll(async () => {
    // Set test environment
    process.env.NODE_ENV = 'test';

    // Clean up database tables in correct order
    await InventoryAudit.destroy({ where: {}, force: true });
    await InventoryItem.destroy({ where: {}, force: true });
    await Product.destroy({ where: {}, force: true });
    await Container.destroy({ where: {}, force: true });
    await User.destroy({ where: {}, force: true });

    // Create test admin user
    adminUser = await User.create({
      username: 'testadmin',
      email: '<EMAIL>',
      password_hash: 'TestPassword123!',
      role: 'admin',
      is_active: true
    });

    // Login to get auth token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        username: 'testadmin',
        password: 'TestPassword123!'
      });

    authToken = loginResponse.body.data.token;

    // Create test product
    testProduct = await Product.create({
      brand: 'Dell',
      model: 'Latitude 7420',
      specifications_json: {
        cpu: 'Intel i7-1185G7',
        ram: '16GB DDR4',
        storage: '512GB SSD',
        screen_size: '14 inch'
      },
      base_price: 1200.00,
      created_by: adminUser.id
    });

    // Create test container
    testContainer = await Container.create({
      container_number: 'CONT001',
      arrival_date: new Date(),
      status: 'arrived',
      total_items: 10,
      created_by: adminUser.id
    });
  });

  afterAll(async () => {
    await sequelize.close();
  });

  describe('POST /api/inventory', () => {
    it('should create a new inventory item successfully', async () => {
      const inventoryData = {
        product_id: testProduct.id,
        container_id: testContainer.id,
        serial_number: 'DELL123456789',
        condition: 'new',
        status: 'available',
        cost_price: 1000.00,
        location: 'A1-B2',
        warehouse: 'main',
        notes: 'Test laptop in perfect condition'
      };

      const response = await request(app)
        .post('/api/inventory')
        .set('Authorization', `Bearer ${authToken}`)
        .send(inventoryData)
        .expect(201);

      expect(response.body.message).toBe('Inventory item created successfully');
      expect(response.body.data.serial_number).toBe(inventoryData.serial_number);
      expect(response.body.data.condition).toBe(inventoryData.condition);
      expect(response.body.data.status).toBe(inventoryData.status);
      expect(response.body.data.location).toBe(inventoryData.location);
      expect(response.body.data.warehouse).toBe(inventoryData.warehouse);

      testInventoryItem = response.body.data;
    });

    it('should reject duplicate serial numbers', async () => {
      const inventoryData = {
        product_id: testProduct.id,
        container_id: testContainer.id,
        serial_number: 'DELL123456789', // Same as above
        condition: 'new',
        cost_price: 1000.00
      };

      const response = await request(app)
        .post('/api/inventory')
        .set('Authorization', `Bearer ${authToken}`)
        .send(inventoryData)
        .expect(409);

      expect(response.body.error).toBe('Duplicate serial number');
    });

    it('should validate required fields', async () => {
      const response = await request(app)
        .post('/api/inventory')
        .set('Authorization', `Bearer ${authToken}`)
        .send({})
        .expect(400);

      expect(response.body.error).toBe('Validation error');
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ field: 'product_id' }),
          expect.objectContaining({ field: 'container_id' }),
          expect.objectContaining({ field: 'serial_number' }),
          expect.objectContaining({ field: 'cost_price' })
        ])
      );
    });

    it('should require authentication', async () => {
      const response = await request(app)
        .post('/api/inventory')
        .send({
          product_id: testProduct.id,
          container_id: testContainer.id,
          serial_number: 'TEST123',
          cost_price: 1000.00
        })
        .expect(401);

      expect(response.body.error).toBe('Access denied');
    });
  });

  describe('GET /api/inventory', () => {
    it('should retrieve inventory items with pagination', async () => {
      const response = await request(app)
        .get('/api/inventory?page=1&limit=10')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.message).toBe('Inventory items retrieved successfully');
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.pagination).toHaveProperty('current_page', 1);
      expect(response.body.pagination).toHaveProperty('total_items');
    });

    it('should filter by status', async () => {
      const response = await request(app)
        .get('/api/inventory?status=available')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      response.body.data.forEach(item => {
        expect(item.status).toBe('available');
      });
    });

    it('should filter by condition', async () => {
      const response = await request(app)
        .get('/api/inventory?condition=new')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      response.body.data.forEach(item => {
        expect(item.condition).toBe('new');
      });
    });

    it('should search by serial number', async () => {
      const response = await request(app)
        .get('/api/inventory?serial_number=DELL123')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      response.body.data.forEach(item => {
        expect(item.serial_number).toContain('DELL123');
      });
    });
  });

  describe('GET /api/inventory/:id', () => {
    it('should retrieve specific inventory item with history', async () => {
      const response = await request(app)
        .get(`/api/inventory/${testInventoryItem.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.id).toBe(testInventoryItem.id);
      expect(response.body.data.serial_number).toBe(testInventoryItem.serial_number);
      expect(response.body.data).toHaveProperty('product');
      expect(response.body.data).toHaveProperty('container');
      expect(response.body.data).toHaveProperty('auditHistory');
      expect(response.body.data).toHaveProperty('days_in_status');
      expect(response.body.data).toHaveProperty('is_available');
      expect(response.body.data).toHaveProperty('barcode_data');
    });

    it('should return 404 for non-existent item', async () => {
      const response = await request(app)
        .get('/api/inventory/99999')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body.error).toBe('Inventory item not found');
    });
  });

  describe('PUT /api/inventory/:id', () => {
    it('should update inventory item status with audit trail', async () => {
      const updateData = {
        status: 'reserved',
        change_reason: 'Reserved for customer order'
      };

      const response = await request(app)
        .put(`/api/inventory/${testInventoryItem.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.data.status).toBe('reserved');

      // Check audit trail was created
      const auditRecords = await InventoryAudit.findAll({
        where: { 
          inventory_item_id: testInventoryItem.id,
          field_name: 'status'
        }
      });

      expect(auditRecords.length).toBeGreaterThan(0);
      expect(auditRecords[0].new_value).toBe('reserved');
      expect(auditRecords[0].change_reason).toBe(updateData.change_reason);
    });

    it('should validate status transitions', async () => {
      // Try to transition from reserved to sold (valid)
      const validTransition = await request(app)
        .put(`/api/inventory/${testInventoryItem.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ status: 'sold' })
        .expect(200);

      expect(validTransition.body.data.status).toBe('sold');

      // Try invalid transition from sold to available (should fail)
      const invalidTransition = await request(app)
        .put(`/api/inventory/${testInventoryItem.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ status: 'available' })
        .expect(400);

      expect(invalidTransition.body.error).toBe('Invalid status transition');
    });
  });

  describe('GET /api/inventory/reports', () => {
    it('should generate summary report', async () => {
      const response = await request(app)
        .get('/api/inventory/reports?report_type=summary')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.report_type).toBe('summary');
      expect(response.body.data.summary).toHaveProperty('total');
      expect(response.body.data.summary).toHaveProperty('by_status');
      expect(response.body.data.summary).toHaveProperty('by_condition');
      expect(response.body.data.summary).toHaveProperty('by_warehouse');
      expect(response.body.data.summary).toHaveProperty('value');
    });

    it('should generate aging report', async () => {
      const response = await request(app)
        .get('/api/inventory/reports?report_type=aging')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.report_type).toBe('aging');
      expect(response.body.data.aging_buckets).toHaveProperty('0-30');
      expect(response.body.data.aging_buckets).toHaveProperty('31-60');
      expect(response.body.data.aging_buckets).toHaveProperty('61-90');
      expect(response.body.data.aging_buckets).toHaveProperty('91-180');
      expect(response.body.data.aging_buckets).toHaveProperty('180+');
    });

    it('should generate low stock report', async () => {
      const response = await request(app)
        .get('/api/inventory/reports?report_type=low_stock')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.report_type).toBe('low_stock');
      expect(response.body.data).toHaveProperty('alerts');
      expect(response.body.data).toHaveProperty('total_products_checked');
      expect(response.body.data).toHaveProperty('products_with_alerts');
    });

    it('should require manager or admin role for reports', async () => {
      // Create employee user
      const employeeUser = await User.create({
        username: 'employee',
        email: '<EMAIL>',
        password_hash: 'TestPassword123!',
        role: 'employee',
        is_active: true
      });

      const employeeLogin = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'employee',
          password: 'TestPassword123!'
        });

      const employeeToken = employeeLogin.body.data.token;

      const response = await request(app)
        .get('/api/inventory/reports')
        .set('Authorization', `Bearer ${employeeToken}`)
        .expect(403);

      expect(response.body.error).toBe('Forbidden');
    });
  });

  describe('POST /api/inventory/:id/barcode', () => {
    it('should generate barcode data', async () => {
      const response = await request(app)
        .post(`/api/inventory/${testInventoryItem.id}/barcode`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ type: 'barcode' })
        .expect(200);

      expect(response.body.message).toBe('Barcode generated successfully');
      expect(response.body.data.code_data.type).toBe('barcode');
      expect(response.body.data.code_data.data).toHaveProperty('serial_number');
      expect(response.body.data.code_data.data).toHaveProperty('product_id');
      expect(response.body.data.code_data.data).toHaveProperty('id');
    });

    it('should generate QR code data', async () => {
      const response = await request(app)
        .post(`/api/inventory/${testInventoryItem.id}/barcode`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ type: 'qr' })
        .expect(200);

      expect(response.body.message).toBe('QR code generated successfully');
      expect(response.body.data.code_data.type).toBe('qr_code');
      expect(response.body.data.code_data.format).toBe('json');
      
      const qrData = JSON.parse(response.body.data.code_data.data);
      expect(qrData.type).toBe('laptop_inventory');
      expect(qrData.serial).toBe(testInventoryItem.serial_number);
    });
  });
});
