const request = require('supertest');
const app = require('../server');
const { User } = require('../src/models');
const { testDatabaseConnection, closeDatabaseConnection } = require('../src/utils/dbConnection');

describe('Authentication System', () => {
  let adminToken, managerToken, employeeToken;
  let adminUser, managerUser, employeeUser;

  beforeAll(async () => {
    // Ensure database connection
    const dbStatus = await testDatabaseConnection();
    expect(dbStatus.status).toBe('connected');

    // Get existing users from seeder
    adminUser = await User.findOne({ where: { username: 'admin' } });
    managerUser = await User.findOne({ where: { username: 'manager1' } });
    employeeUser = await User.findOne({ where: { username: 'employee1' } });

    expect(adminUser).toBeTruthy();
    expect(managerUser).toBeTruthy();
    expect(employeeUser).toBeTruthy();
  });

  afterAll(async () => {
    await closeDatabaseConnection();
  });

  describe('POST /api/auth/register', () => {
    it('should register a new user successfully', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        role: 'employee'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body).toHaveProperty('message', 'User registered successfully');
      expect(response.body).toHaveProperty('user');
      expect(response.body).toHaveProperty('token');
      expect(response.body.user.username).toBe(userData.username);
      expect(response.body.user.email).toBe(userData.email);
      expect(response.body.user.role).toBe(userData.role);
      expect(response.body.user).not.toHaveProperty('password_hash');
    });

    it('should fail with invalid password', async () => {
      const userData = {
        username: 'testuser2',
        email: '<EMAIL>',
        password: 'weak', // Too weak
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Validation error');
    });

    it('should fail with duplicate username', async () => {
      const userData = {
        username: 'admin', // Already exists
        email: '<EMAIL>',
        password: 'TestPassword123!',
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body).toHaveProperty('error', 'User already exists');
    });

    it('should fail with duplicate email', async () => {
      const userData = {
        username: 'newusername',
        email: '<EMAIL>', // Already exists
        password: 'TestPassword123!',
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body).toHaveProperty('error', 'User already exists');
    });
  });

  describe('POST /api/auth/login', () => {
    it('should login admin user successfully', async () => {
      const loginData = {
        username: 'admin',
        password: 'Admin123!@#'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body).toHaveProperty('message', 'Login successful');
      expect(response.body).toHaveProperty('user');
      expect(response.body).toHaveProperty('token');
      expect(response.body.user.username).toBe('admin');
      expect(response.body.user.role).toBe('admin');

      adminToken = response.body.token;
    });

    it('should login manager user successfully', async () => {
      const loginData = {
        username: 'manager1',
        password: 'Manager123!@#'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body).toHaveProperty('token');
      managerToken = response.body.token;
    });

    it('should login employee user successfully', async () => {
      const loginData = {
        username: 'employee1',
        password: 'Employee123!@#'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body).toHaveProperty('token');
      employeeToken = response.body.token;
    });

    it('should login with email instead of username', async () => {
      const loginData = {
        username: '<EMAIL>', // Using email
        password: 'Admin123!@#'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body).toHaveProperty('token');
    });

    it('should fail with invalid credentials', async () => {
      const loginData = {
        username: 'admin',
        password: 'wrongpassword'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body).toHaveProperty('error', 'Authentication failed');
    });

    it('should fail with non-existent user', async () => {
      const loginData = {
        username: 'nonexistent',
        password: 'TestPassword123!'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body).toHaveProperty('error', 'Authentication failed');
    });
  });

  describe('GET /api/auth/profile', () => {
    it('should get user profile with valid token', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('user');
      expect(response.body.user.username).toBe('admin');
      expect(response.body.user).not.toHaveProperty('password_hash');
    });

    it('should fail without token', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .expect(401);

      expect(response.body).toHaveProperty('error', 'Access denied');
    });

    it('should fail with invalid token', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', 'Bearer invalidtoken')
        .expect(403);

      expect(response.body).toHaveProperty('error', 'Invalid token');
    });
  });

  describe('POST /api/auth/logout', () => {
    it('should logout successfully', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('message', 'Logout successful');
    });
  });

  describe('Password Reset Flow', () => {
    let resetToken;

    it('should request password reset', async () => {
      const response = await request(app)
        .post('/api/auth/forgot-password')
        .send({ email: '<EMAIL>' })
        .expect(200);

      expect(response.body).toHaveProperty('message');
      // In development, the token is returned for testing
      if (response.body.resetToken) {
        resetToken = response.body.resetToken;
      }
    });

    it('should not reveal if email does not exist', async () => {
      const response = await request(app)
        .post('/api/auth/forgot-password')
        .send({ email: '<EMAIL>' })
        .expect(200);

      expect(response.body).toHaveProperty('message');
    });

    it('should reset password with valid token', async () => {
      if (resetToken) {
        const response = await request(app)
          .post('/api/auth/reset-password')
          .send({
            token: resetToken,
            newPassword: 'NewPassword123!'
          })
          .expect(200);

        expect(response.body).toHaveProperty('message', 'Password reset successful');
      }
    });

    it('should fail with invalid reset token', async () => {
      const response = await request(app)
        .post('/api/auth/reset-password')
        .send({
          token: 'invalidtoken',
          newPassword: 'NewPassword123!'
        })
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Invalid or expired token');
    });
  });

  describe('Rate Limiting', () => {
    it('should apply rate limiting to login attempts', async () => {
      const loginData = {
        username: 'admin',
        password: 'wrongpassword'
      };

      // Make multiple failed login attempts
      for (let i = 0; i < 6; i++) {
        await request(app)
          .post('/api/auth/login')
          .send(loginData);
      }

      // The 6th attempt should be rate limited
      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(429);

      expect(response.body).toHaveProperty('error', 'Too many authentication attempts');
    }, 10000);
  });
});
