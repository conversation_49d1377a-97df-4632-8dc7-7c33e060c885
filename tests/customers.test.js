const request = require('supertest');
const app = require('../server');
const { Customer, CustomerCommunication, User } = require('../src/models');
const { sequelize } = require('../src/models');

describe('Customer Management API', () => {
  let authToken;
  let adminUser;
  let testCustomer;

  beforeAll(async () => {
    // Create test admin user
    adminUser = await User.create({
      username: 'testadmin',
      email: '<EMAIL>',
      password_hash: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', // password123
      role: 'admin',
      is_active: true
    });

    // Login to get auth token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        username: 'testadmin',
        password: 'password123'
      });

    authToken = loginResponse.body.token;
  });

  afterAll(async () => {
    // Clean up test data
    await CustomerCommunication.destroy({ where: {}, force: true });
    await Customer.destroy({ where: {}, force: true });
    await User.destroy({ where: {}, force: true });
    await sequelize.close();
  });

  beforeEach(async () => {
    // Clean up before each test
    await CustomerCommunication.destroy({ where: {}, force: true });
    await Customer.destroy({ where: {}, force: true, paranoid: false });
  });

  describe('POST /api/customers', () => {
    it('should create a new customer with valid data', async () => {
      const customerData = {
        company_name: 'Test Electronics Ltd',
        contact_person: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        address: {
          street: '123 Business St',
          city: 'Tech City',
          state: 'CA',
          postal_code: '12345',
          country: 'USA'
        },
        credit_limit: 10000.00,
        pricing_tier: 'silver',
        payment_terms: 45
      };

      const response = await request(app)
        .post('/api/customers')
        .set('Authorization', `Bearer ${authToken}`)
        .send(customerData)
        .expect(201);

      expect(response.body.message).toBe('Customer created successfully');
      expect(response.body.customer.company_name).toBe(customerData.company_name);
      expect(response.body.customer.contact_person).toBe(customerData.contact_person);
      expect(response.body.customer.email).toBe(customerData.email);
      expect(response.body.customer.credit_limit).toBe('10000.00');
      expect(response.body.customer.pricing_tier).toBe('silver');

      // Verify customer was created in database
      const customer = await Customer.findOne({ where: { email: customerData.email } });
      expect(customer).toBeTruthy();
      expect(customer.company_name).toBe(customerData.company_name);
    });

    it('should reject duplicate email addresses', async () => {
      // Create first customer
      await Customer.create({
        company_name: 'First Company',
        contact_person: 'Jane Doe',
        email: '<EMAIL>',
        credit_limit: 5000.00
      });

      // Try to create second customer with same email
      const response = await request(app)
        .post('/api/customers')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          company_name: 'Second Company',
          contact_person: 'John Smith',
          email: '<EMAIL>'
        })
        .expect(409);

      expect(response.body.error).toBe('Duplicate email');
    });

    it('should validate required fields', async () => {
      const response = await request(app)
        .post('/api/customers')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          company_name: 'Test Company'
          // Missing required fields
        })
        .expect(400);

      expect(response.body.error).toBe('Validation error');
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ field: 'contact_person' }),
          expect.objectContaining({ field: 'email' })
        ])
      );
    });

    it('should require admin or manager role', async () => {
      // Create employee user
      const employeeUser = await User.create({
        username: 'employee',
        email: '<EMAIL>',
        password_hash: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm',
        role: 'employee',
        is_active: true
      });

      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'employee',
          password: 'password123'
        });

      const employeeToken = loginResponse.body.token;

      const response = await request(app)
        .post('/api/customers')
        .set('Authorization', `Bearer ${employeeToken}`)
        .send({
          company_name: 'Test Company',
          contact_person: 'John Doe',
          email: '<EMAIL>'
        })
        .expect(403);

      expect(response.body.error).toBe('Forbidden');
    });
  });

  describe('GET /api/customers', () => {
    beforeEach(async () => {
      // Create test customers
      await Customer.bulkCreate([
        {
          company_name: 'Alpha Electronics',
          contact_person: 'Alice Johnson',
          email: '<EMAIL>',
          pricing_tier: 'bronze',
          status: 'active',
          credit_limit: 5000.00
        },
        {
          company_name: 'Beta Systems',
          contact_person: 'Bob Wilson',
          email: '<EMAIL>',
          pricing_tier: 'silver',
          status: 'active',
          credit_limit: 15000.00
        },
        {
          company_name: 'Gamma Corp',
          contact_person: 'Carol Davis',
          email: '<EMAIL>',
          pricing_tier: 'gold',
          status: 'inactive',
          credit_limit: 25000.00
        }
      ]);
    });

    it('should list all customers with pagination', async () => {
      const response = await request(app)
        .get('/api/customers?page=1&limit=2')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.customers).toHaveLength(2);
      expect(response.body.pagination.total).toBe(3);
      expect(response.body.pagination.page).toBe(1);
      expect(response.body.pagination.limit).toBe(2);
      expect(response.body.pagination.totalPages).toBe(2);
    });

    it('should filter customers by pricing tier', async () => {
      const response = await request(app)
        .get('/api/customers?pricing_tier=silver')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.customers).toHaveLength(1);
      expect(response.body.customers[0].pricing_tier).toBe('silver');
    });

    it('should filter customers by status', async () => {
      const response = await request(app)
        .get('/api/customers?status=inactive')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.customers).toHaveLength(1);
      expect(response.body.customers[0].status).toBe('inactive');
    });

    it('should search customers by company name and contact person', async () => {
      const response = await request(app)
        .get('/api/customers?search=Alpha')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.customers).toHaveLength(1);
      expect(response.body.customers[0].company_name).toBe('Alpha Electronics');
    });

    it('should filter by credit limit range', async () => {
      const response = await request(app)
        .get('/api/customers?credit_limit_min=10000&credit_limit_max=20000')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.customers).toHaveLength(1);
      expect(response.body.customers[0].company_name).toBe('Beta Systems');
    });

    it('should include summary statistics for admin/manager', async () => {
      const response = await request(app)
        .get('/api/customers')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.summary).toBeDefined();
      expect(response.body.summary.total_customers).toBe(3);
      expect(response.body.summary.total_credit_limit).toBe(45000);
    });
  });

  describe('GET /api/customers/:id', () => {
    beforeEach(async () => {
      testCustomer = await Customer.create({
        company_name: 'Test Customer Inc',
        contact_person: 'Test Person',
        email: '<EMAIL>',
        pricing_tier: 'bronze',
        status: 'active',
        credit_limit: 10000.00,
        credit_used: 2500.00
      });
    });

    it('should return detailed customer profile', async () => {
      const response = await request(app)
        .get(`/api/customers/${testCustomer.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.customer.id).toBe(testCustomer.id);
      expect(response.body.customer.company_name).toBe('Test Customer Inc');
      expect(response.body.customer.credit_utilization).toBe(25); // 2500/10000 * 100
      expect(response.body.customer.available_credit).toBe(7500);
      expect(response.body.analytics).toBeDefined();
    });

    it('should return 404 for non-existent customer', async () => {
      const response = await request(app)
        .get('/api/customers/99999')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body.error).toBe('Customer not found');
    });
  });

  describe('PUT /api/customers/:id', () => {
    beforeEach(async () => {
      testCustomer = await Customer.create({
        company_name: 'Test Customer Inc',
        contact_person: 'Test Person',
        email: '<EMAIL>',
        pricing_tier: 'bronze',
        status: 'active',
        credit_limit: 10000.00
      });
    });

    it('should update customer information', async () => {
      const updateData = {
        company_name: 'Updated Customer Inc',
        contact_person: 'Updated Person',
        pricing_tier: 'silver'
      };

      const response = await request(app)
        .put(`/api/customers/${testCustomer.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.message).toBe('Customer updated successfully');
      expect(response.body.customer.company_name).toBe('Updated Customer Inc');
      expect(response.body.customer.pricing_tier).toBe('silver');
      expect(response.body.changes).toEqual(
        expect.arrayContaining([
          expect.stringContaining('company_name'),
          expect.stringContaining('pricing_tier')
        ])
      );
    });

    it('should prevent duplicate email updates', async () => {
      // Create another customer
      await Customer.create({
        company_name: 'Other Customer',
        contact_person: 'Other Person',
        email: '<EMAIL>',
        credit_limit: 5000.00
      });

      const response = await request(app)
        .put(`/api/customers/${testCustomer.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ email: '<EMAIL>' })
        .expect(409);

      expect(response.body.error).toBe('Duplicate email');
    });
  });
});
