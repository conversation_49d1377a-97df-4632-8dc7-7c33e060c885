const request = require('supertest');
const app = require('../server');
const { sequelize, User, Customer, Product, Container, InventoryItem, SalesOrder, OrderItem } = require('../src/models');

describe('Sales Order Processing System', () => {
  let authToken;
  let testUser;
  let testCustomer;
  let testProduct;
  let testContainer;
  let testInventoryItems = [];

  beforeAll(async () => {
    // Ensure database connection
    await sequelize.authenticate();
  });

  beforeEach(async () => {
    // Clean up database in proper order to avoid foreign key constraints
    await OrderItem.destroy({ where: {}, force: true, cascade: true });
    await SalesOrder.destroy({ where: {}, force: true, cascade: true });
    await InventoryItem.destroy({ where: {}, force: true, cascade: true });
    await Product.destroy({ where: {}, force: true, cascade: true });
    await Container.destroy({ where: {}, force: true, cascade: true });
    await Customer.destroy({ where: {}, force: true, cascade: true });
    await User.destroy({ where: {}, force: true, cascade: true });

    // Create test user using registration endpoint
    const registrationResponse = await request(app)
      .post('/api/auth/register')
      .send({
        username: 'salestest',
        email: '<EMAIL>',
        password: 'SalesTest123!',
        role: 'manager'
      });

    authToken = registrationResponse.body.token;
    testUser = await User.findOne({ where: { username: 'salestest' } });

    // Create test customer
    testCustomer = await Customer.create({
      company_name: 'Test Retailer Inc',
      contact_info: {
        email: '<EMAIL>',
        phone: '******-0123',
        address: {
          street: '123 Business Ave',
          city: 'Commerce City',
          state: 'CA',
          zip: '90210',
          country: 'USA'
        }
      },
      credit_limit: 50000.00,
      credit_used: 0.00,
      pricing_tier: 'gold',
      status: 'active'
    });

    // Create test container
    testContainer = await Container.create({
      container_number: 'SALES-TEST-001',
      arrival_date: new Date(),
      status: 'completed',
      total_items: 10
    });

    // Create test product
    testProduct = await Product.create({
      brand: 'Dell',
      model: 'Latitude 7420',
      specifications_json: {
        processor: 'Intel i7-1185G7',
        ram: '16GB DDR4',
        storage: '512GB SSD',
        display: '14" FHD',
        graphics: 'Intel Iris Xe'
      },
      base_price: 1200.00
    });

    // Create test inventory items
    for (let i = 1; i <= 5; i++) {
      const inventoryItem = await InventoryItem.create({
        product_id: testProduct.id,
        container_id: testContainer.id,
        serial_number: `SALES-TEST-${i.toString().padStart(3, '0')}`,
        condition: 'new',
        status: 'available',
        cost_price: 1000.00,
        warehouse: 'WH-A',
        location: `A-${i}-1`
      });
      testInventoryItems.push(inventoryItem);
    }
  });

  afterAll(async () => {
    await sequelize.close();
  });

  describe('POST /api/orders/quote', () => {
    it('should generate a quote for valid customer and items', async () => {
      const response = await request(app)
        .post('/api/orders/quote')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          customer_id: testCustomer.id,
          items: [
            {
              product_id: testProduct.id,
              quantity: 2
            }
          ]
        });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Quote generated successfully');
      expect(response.body.quote).toHaveProperty('customer');
      expect(response.body.quote).toHaveProperty('items');
      expect(response.body.quote).toHaveProperty('availability');
      expect(response.body.quote).toHaveProperty('pricing');
      expect(response.body.quote).toHaveProperty('credit_validation');
      expect(response.body.quote.can_fulfill).toBe(true);
      
      // Check pricing calculations (gold tier = 10% discount)
      const expectedPrice = 1200.00 * 0.9; // 10% discount for gold tier
      expect(response.body.quote.items[0].unit_price).toBe(expectedPrice);
    });

    it('should return error for non-existent customer', async () => {
      const response = await request(app)
        .post('/api/orders/quote')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          customer_id: 99999,
          items: [
            {
              product_id: testProduct.id,
              quantity: 1
            }
          ]
        });

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Not found');
    });

    it('should handle insufficient inventory in quote', async () => {
      const response = await request(app)
        .post('/api/orders/quote')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          customer_id: testCustomer.id,
          items: [
            {
              product_id: testProduct.id,
              quantity: 10 // More than available (5)
            }
          ]
        });

      expect(response.status).toBe(200);
      expect(response.body.quote.can_fulfill).toBe(false);
      expect(response.body.quote.availability[0].is_available).toBe(false);
      expect(response.body.quote.availability[0].shortage).toBe(5);
    });
  });

  describe('POST /api/orders', () => {
    it('should create a sales order successfully', async () => {
      const response = await request(app)
        .post('/api/orders')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          customer_id: testCustomer.id,
          items: [
            {
              product_id: testProduct.id,
              quantity: 2
            }
          ],
          notes: 'Test order creation'
        });

      expect(response.status).toBe(201);
      expect(response.body.message).toBe('Sales order created successfully');
      expect(response.body.order.order.status).toBe('pending');
      expect(response.body.order.order.customer_id).toBe(testCustomer.id);
      expect(response.body.order.items).toHaveLength(2);

      // Verify inventory items are reserved
      const reservedItems = await InventoryItem.findAll({
        where: { status: 'reserved' }
      });
      expect(reservedItems).toHaveLength(2);
    });

    it('should reject order with insufficient inventory', async () => {
      const response = await request(app)
        .post('/api/orders')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          customer_id: testCustomer.id,
          items: [
            {
              product_id: testProduct.id,
              quantity: 10 // More than available
            }
          ]
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Insufficient inventory');
    });

    it('should reject order exceeding credit limit', async () => {
      // Update customer to have low credit limit
      await testCustomer.update({ credit_limit: 1000.00 });

      const response = await request(app)
        .post('/api/orders')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          customer_id: testCustomer.id,
          items: [
            {
              product_id: testProduct.id,
              quantity: 2 // Total would be ~2160 (after tax), exceeding 1000 limit
            }
          ]
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Credit limit exceeded');
    });
  });

  describe('GET /api/orders', () => {
    let testOrder;

    beforeEach(async () => {
      // Create a test order
      testOrder = await SalesOrder.create({
        customer_id: testCustomer.id,
        order_date: new Date(),
        status: 'pending',
        total_amount: 2400.00
      });

      await OrderItem.create({
        order_id: testOrder.id,
        inventory_item_id: testInventoryItems[0].id,
        quantity: 1,
        unit_price: 1200.00
      });
    });

    it('should retrieve orders with pagination', async () => {
      const response = await request(app)
        .get('/api/orders')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          page: 1,
          limit: 10
        });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Sales orders retrieved successfully');
      expect(response.body.orders).toHaveLength(1);
      expect(response.body.pagination.total_orders).toBe(1);
    });

    it('should filter orders by status', async () => {
      const response = await request(app)
        .get('/api/orders')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          status: 'pending'
        });

      expect(response.status).toBe(200);
      expect(response.body.orders).toHaveLength(1);
      expect(response.body.orders[0].status).toBe('pending');
    });

    it('should filter orders by customer', async () => {
      const response = await request(app)
        .get('/api/orders')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          customer_id: testCustomer.id
        });

      expect(response.status).toBe(200);
      expect(response.body.orders).toHaveLength(1);
      expect(response.body.orders[0].customer_id).toBe(testCustomer.id);
    });
  });

  describe('GET /api/orders/:id', () => {
    let testOrder;

    beforeEach(async () => {
      testOrder = await SalesOrder.create({
        customer_id: testCustomer.id,
        order_date: new Date(),
        status: 'pending',
        total_amount: 1200.00
      });

      await OrderItem.create({
        order_id: testOrder.id,
        inventory_item_id: testInventoryItems[0].id,
        quantity: 1,
        unit_price: 1200.00
      });
    });

    it('should retrieve order by ID with full details', async () => {
      const response = await request(app)
        .get(`/api/orders/${testOrder.id}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Sales order retrieved successfully');
      expect(response.body.order.id).toBe(testOrder.id);
      expect(response.body.order.customer).toBeDefined();
      expect(response.body.order.orderItems).toHaveLength(1);
    });

    it('should return 404 for non-existent order', async () => {
      const response = await request(app)
        .get('/api/orders/99999')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Order not found');
    });
  });

  describe('PUT /api/orders/:id/status', () => {
    let testOrder;

    beforeEach(async () => {
      testOrder = await SalesOrder.create({
        customer_id: testCustomer.id,
        order_date: new Date(),
        status: 'pending',
        total_amount: 1200.00
      });

      // Reserve an inventory item
      await testInventoryItems[0].update({ status: 'reserved' });

      await OrderItem.create({
        order_id: testOrder.id,
        inventory_item_id: testInventoryItems[0].id,
        quantity: 1,
        unit_price: 1200.00
      });
    });

    it('should update order status successfully', async () => {
      const response = await request(app)
        .put(`/api/orders/${testOrder.id}/status`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          status: 'confirmed',
          notes: 'Order confirmed by manager'
        });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Order status updated successfully');
      expect(response.body.order.status).toBe('confirmed');
      expect(response.body.status_change.from).toBe('pending');
      expect(response.body.status_change.to).toBe('confirmed');
    });

    it('should reject invalid status transition', async () => {
      const response = await request(app)
        .put(`/api/orders/${testOrder.id}/status`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          status: 'delivered' // Cannot go directly from pending to delivered
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Invalid status transition');
    });

    it('should release inventory when order is cancelled', async () => {
      const response = await request(app)
        .put(`/api/orders/${testOrder.id}/status`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          status: 'cancelled',
          notes: 'Customer requested cancellation'
        });

      expect(response.status).toBe(200);
      expect(response.body.order.status).toBe('cancelled');

      // Verify inventory item is released
      await testInventoryItems[0].reload();
      expect(testInventoryItems[0].status).toBe('available');
    });
  });

  describe('POST /api/orders/:id/fulfill', () => {
    let testOrder;

    beforeEach(async () => {
      testOrder = await SalesOrder.create({
        customer_id: testCustomer.id,
        order_date: new Date(),
        status: 'confirmed',
        total_amount: 1200.00
      });

      // Reserve an inventory item
      await testInventoryItems[0].update({ status: 'reserved' });

      await OrderItem.create({
        order_id: testOrder.id,
        inventory_item_id: testInventoryItems[0].id,
        quantity: 1,
        unit_price: 1200.00
      });
    });

    it('should fulfill order successfully', async () => {
      const response = await request(app)
        .post(`/api/orders/${testOrder.id}/fulfill`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          shipping_info: {
            tracking_number: 'TRK123456789',
            carrier: 'FedEx',
            estimated_delivery: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString()
          },
          notes: 'Order shipped via FedEx'
        });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Order fulfilled successfully');
      expect(response.body.order.status).toBe('shipped');
      expect(response.body.fulfillment_details.items_fulfilled).toBe(1);

      // Verify inventory item is marked as sold
      await testInventoryItems[0].reload();
      expect(testInventoryItems[0].status).toBe('sold');
    });

    it('should reject fulfillment for invalid order status', async () => {
      // Update order to pending status
      await testOrder.update({ status: 'pending' });

      const response = await request(app)
        .post(`/api/orders/${testOrder.id}/fulfill`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          shipping_info: {
            tracking_number: 'TRK123456789',
            carrier: 'FedEx'
          }
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Invalid order status');
    });

    it('should return 404 for non-existent order', async () => {
      const response = await request(app)
        .post('/api/orders/99999/fulfill')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          shipping_info: {
            tracking_number: 'TRK123456789',
            carrier: 'FedEx'
          }
        });

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Order not found');
    });
  });

  describe('Authorization Tests', () => {
    let employeeToken;

    beforeEach(async () => {
      // Create employee user using registration endpoint
      const registrationResponse = await request(app)
        .post('/api/auth/register')
        .send({
          username: 'employee',
          email: '<EMAIL>',
          password: 'Employee123!',
          role: 'employee'
        });

      employeeToken = registrationResponse.body.token;
    });

    it('should allow employees to view orders', async () => {
      const response = await request(app)
        .get('/api/orders')
        .set('Authorization', `Bearer ${employeeToken}`);

      expect(response.status).toBe(200);
    });

    it('should prevent employees from creating orders', async () => {
      const response = await request(app)
        .post('/api/orders')
        .set('Authorization', `Bearer ${employeeToken}`)
        .send({
          customer_id: testCustomer.id,
          items: [
            {
              product_id: testProduct.id,
              quantity: 1
            }
          ]
        });

      expect(response.status).toBe(403);
      expect(response.body.error).toBe('Forbidden');
    });

    it('should prevent employees from updating order status', async () => {
      const testOrder = await SalesOrder.create({
        customer_id: testCustomer.id,
        order_date: new Date(),
        status: 'pending',
        total_amount: 1200.00
      });

      const response = await request(app)
        .put(`/api/orders/${testOrder.id}/status`)
        .set('Authorization', `Bearer ${employeeToken}`)
        .send({
          status: 'confirmed'
        });

      expect(response.status).toBe(403);
      expect(response.body.error).toBe('Forbidden');
    });
  });
});
