const request = require('supertest');
const app = require('../server');
const { User } = require('../src/models');
const { testDatabaseConnection, closeDatabaseConnection } = require('../src/utils/dbConnection');

describe('User Management System', () => {
  let adminToken, managerToken, employeeToken;
  let adminUser, managerUser, employeeUser, testUser;

  beforeAll(async () => {
    // Ensure database connection
    const dbStatus = await testDatabaseConnection();
    expect(dbStatus.status).toBe('connected');

    // Login to get tokens
    const adminLogin = await request(app)
      .post('/api/auth/login')
      .send({ username: 'admin', password: 'Admin123!@#' });
    adminToken = adminLogin.body.token;

    const managerLogin = await request(app)
      .post('/api/auth/login')
      .send({ username: 'manager1', password: 'Manager123!@#' });
    managerToken = managerLogin.body.token;

    const employeeLogin = await request(app)
      .post('/api/auth/login')
      .send({ username: 'employee1', password: 'Employee123!@#' });
    employeeToken = employeeLogin.body.token;

    // Get user objects
    adminUser = await User.findOne({ where: { username: 'admin' } });
    managerUser = await User.findOne({ where: { username: 'manager1' } });
    employeeUser = await User.findOne({ where: { username: 'employee1' } });

    // Create a test user for manipulation
    testUser = await User.create({
      username: 'testuser123',
      email: '<EMAIL>',
      password_hash: 'TestPassword123!',
      role: 'employee'
    });
  });

  afterAll(async () => {
    // Clean up test user
    if (testUser) {
      await testUser.destroy();
    }
    await closeDatabaseConnection();
  });

  describe('GET /api/users', () => {
    it('should allow admin to get all users', async () => {
      const response = await request(app)
        .get('/api/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('users');
      expect(response.body).toHaveProperty('pagination');
      expect(Array.isArray(response.body.users)).toBe(true);
      expect(response.body.users.length).toBeGreaterThan(0);
    });

    it('should allow manager to get all users', async () => {
      const response = await request(app)
        .get('/api/users')
        .set('Authorization', `Bearer ${managerToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('users');
    });

    it('should deny employee access to user list', async () => {
      const response = await request(app)
        .get('/api/users')
        .set('Authorization', `Bearer ${employeeToken}`)
        .expect(403);

      expect(response.body).toHaveProperty('error', 'Forbidden');
    });

    it('should support pagination', async () => {
      const response = await request(app)
        .get('/api/users?page=1&limit=2')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.pagination.page).toBe(1);
      expect(response.body.pagination.limit).toBe(2);
      expect(response.body.users.length).toBeLessThanOrEqual(2);
    });

    it('should support role filtering', async () => {
      const response = await request(app)
        .get('/api/users?role=admin')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      response.body.users.forEach(user => {
        expect(user.role).toBe('admin');
      });
    });

    it('should support search functionality', async () => {
      const response = await request(app)
        .get('/api/users?search=admin')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.users.length).toBeGreaterThan(0);
    });
  });

  describe('GET /api/users/stats', () => {
    it('should allow admin to get user statistics', async () => {
      const response = await request(app)
        .get('/api/users/stats')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('totalUsers');
      expect(response.body).toHaveProperty('activeUsers');
      expect(response.body).toHaveProperty('inactiveUsers');
      expect(response.body).toHaveProperty('roleDistribution');
    });

    it('should allow manager to get user statistics', async () => {
      const response = await request(app)
        .get('/api/users/stats')
        .set('Authorization', `Bearer ${managerToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('totalUsers');
    });

    it('should deny employee access to user statistics', async () => {
      const response = await request(app)
        .get('/api/users/stats')
        .set('Authorization', `Bearer ${employeeToken}`)
        .expect(403);

      expect(response.body).toHaveProperty('error', 'Forbidden');
    });
  });

  describe('GET /api/users/:id', () => {
    it('should allow user to get their own profile', async () => {
      const response = await request(app)
        .get(`/api/users/${employeeUser.id}`)
        .set('Authorization', `Bearer ${employeeToken}`)
        .expect(200);

      expect(response.body.user.id).toBe(employeeUser.id);
      expect(response.body.user).not.toHaveProperty('password_hash');
    });

    it('should allow admin to get any user profile', async () => {
      const response = await request(app)
        .get(`/api/users/${employeeUser.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.user.id).toBe(employeeUser.id);
    });

    it('should deny employee access to other user profiles', async () => {
      const response = await request(app)
        .get(`/api/users/${adminUser.id}`)
        .set('Authorization', `Bearer ${employeeToken}`)
        .expect(403);

      expect(response.body).toHaveProperty('error', 'Forbidden');
    });

    it('should return 404 for non-existent user', async () => {
      const fakeId = '123e4567-e89b-12d3-a456-426614174000';
      const response = await request(app)
        .get(`/api/users/${fakeId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);

      expect(response.body).toHaveProperty('error', 'User not found');
    });
  });

  describe('PUT /api/users/:id', () => {
    it('should allow user to update their own profile', async () => {
      const updateData = {
        username: 'updatedemployee',
        email: '<EMAIL>'
      };

      const response = await request(app)
        .put(`/api/users/${testUser.id}`)
        .set('Authorization', `Bearer ${adminToken}`) // Using admin token for test user
        .send(updateData)
        .expect(200);

      expect(response.body.user.username).toBe(updateData.username);
      expect(response.body.user.email).toBe(updateData.email);
    });

    it('should allow admin to change user role', async () => {
      const updateData = {
        role: 'manager'
      };

      const response = await request(app)
        .put(`/api/users/${testUser.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.user.role).toBe('manager');
    });

    it('should deny non-admin from changing roles', async () => {
      const updateData = {
        role: 'admin'
      };

      const response = await request(app)
        .put(`/api/users/${testUser.id}`)
        .set('Authorization', `Bearer ${managerToken}`)
        .send(updateData)
        .expect(403);

      expect(response.body).toHaveProperty('error', 'Forbidden');
    });

    it('should deny employee from updating other users', async () => {
      const updateData = {
        username: 'hacker'
      };

      const response = await request(app)
        .put(`/api/users/${adminUser.id}`)
        .set('Authorization', `Bearer ${employeeToken}`)
        .send(updateData)
        .expect(403);

      expect(response.body).toHaveProperty('error', 'Forbidden');
    });
  });

  describe('PUT /api/users/:id/password', () => {
    it('should allow user to change their own password', async () => {
      // First, we need to login as the test user to get their token
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({ username: 'updatedemployee', password: 'TestPassword123!' });
      
      const testUserToken = loginResponse.body.token;

      const response = await request(app)
        .put(`/api/users/${testUser.id}/password`)
        .set('Authorization', `Bearer ${testUserToken}`)
        .send({
          currentPassword: 'TestPassword123!',
          newPassword: 'NewTestPassword123!'
        })
        .expect(200);

      expect(response.body).toHaveProperty('message', 'Password changed successfully');
    });

    it('should fail with incorrect current password', async () => {
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({ username: 'updatedemployee', password: 'NewTestPassword123!' });
      
      const testUserToken = loginResponse.body.token;

      const response = await request(app)
        .put(`/api/users/${testUser.id}/password`)
        .set('Authorization', `Bearer ${testUserToken}`)
        .send({
          currentPassword: 'wrongpassword',
          newPassword: 'AnotherPassword123!'
        })
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Invalid password');
    });

    it('should deny changing other user passwords', async () => {
      const response = await request(app)
        .put(`/api/users/${adminUser.id}/password`)
        .set('Authorization', `Bearer ${employeeToken}`)
        .send({
          currentPassword: 'Admin123!@#',
          newPassword: 'NewPassword123!'
        })
        .expect(403);

      expect(response.body).toHaveProperty('error', 'Forbidden');
    });
  });

  describe('DELETE /api/users/:id', () => {
    it('should allow admin to deactivate users', async () => {
      const response = await request(app)
        .delete(`/api/users/${testUser.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.user.is_active).toBe(false);
    });

    it('should deny manager from deactivating users', async () => {
      const response = await request(app)
        .delete(`/api/users/${testUser.id}`)
        .set('Authorization', `Bearer ${managerToken}`)
        .expect(403);

      expect(response.body).toHaveProperty('error', 'Forbidden');
    });

    it('should prevent admin from deactivating themselves', async () => {
      const response = await request(app)
        .delete(`/api/users/${adminUser.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Bad request');
    });
  });

  describe('Authentication Middleware', () => {
    it('should reject requests without token', async () => {
      const response = await request(app)
        .get('/api/users')
        .expect(401);

      expect(response.body).toHaveProperty('error', 'Access denied');
    });

    it('should reject requests with malformed token', async () => {
      const response = await request(app)
        .get('/api/users')
        .set('Authorization', 'Bearer malformed.token.here')
        .expect(403);

      expect(response.body).toHaveProperty('error', 'Invalid token');
    });
  });
});
