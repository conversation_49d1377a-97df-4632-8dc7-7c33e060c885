const request = require('supertest');
const app = require('../server');
const { testDatabaseConnection, closeDatabaseConnection } = require('../src/utils/dbConnection');

describe('Integration Tests - Database and Server', () => {
  afterAll(async () => {
    // Close database connections after all tests
    await closeDatabaseConnection();
  });

  describe('Database Integration', () => {
    it('should connect to database successfully', async () => {
      const result = await testDatabaseConnection();
      expect(result.status).toBe('connected');
      expect(result.database).toContain('inventory_manager'); // Could be test or dev database
      expect(result.dialect).toBe('postgres');
    }, 10000);
  });

  describe('Health Endpoints with Database', () => {
    it('should return health status with database information', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'OK');
      expect(response.body).toHaveProperty('database');
      expect(response.body.database).toHaveProperty('status');
      expect(response.body.database).toHaveProperty('connection_time');
      expect(response.body.database).toHaveProperty('database_name');
      expect(response.body.database).toHaveProperty('host');
      expect(response.body.database).toHaveProperty('port');
      expect(response.body.database).toHaveProperty('dialect', 'postgres');
      expect(response.body.database).toHaveProperty('version');
      expect(response.body.database).toHaveProperty('last_check');
    });

    it('should return API health status with database information', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      expect(response.body).toHaveProperty('message', 'Bulk Laptop Inventory Management System API');
      expect(response.body).toHaveProperty('status');
      expect(response.body).toHaveProperty('database');
      expect(response.body.database).toHaveProperty('status');
      expect(response.body.database).toHaveProperty('connection_time');
      expect(response.body.database).toHaveProperty('database_name');
      expect(response.body.database).toHaveProperty('host');
      expect(response.body.database).toHaveProperty('port');
      expect(response.body.database).toHaveProperty('dialect', 'postgres');
      expect(response.body.database).toHaveProperty('version');
      expect(response.body.database).toHaveProperty('last_check');
    });

    it('should return healthy status when database is connected', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      if (response.body.database.status === 'connected') {
        expect(response.body.status).toBe('healthy');
      } else {
        expect(response.body.status).toBe('degraded');
      }
    });
  });

  describe('Database Connection Timing', () => {
    it('should have reasonable connection times', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      if (response.body.database.status === 'connected') {
        const connectionTime = parseInt(response.body.database.connection_time);
        expect(connectionTime).toBeLessThan(1000); // Should be less than 1 second
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully in health endpoints', async () => {
      // This test verifies that even if database connection fails,
      // the health endpoint still responds (though with error status)
      const response = await request(app).get('/health');
      
      expect(response.status).toBeGreaterThanOrEqual(200);
      expect(response.status).toBeLessThan(600);
      expect(response.body).toHaveProperty('status');
      expect(response.body).toHaveProperty('database');
    });
  });

  describe('Database Configuration Validation', () => {
    it('should use correct test database configuration', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      if (response.body.database.status === 'connected') {
        expect(response.body.database.database_name).toContain('inventory_manager');
        expect(response.body.database.host).toBe('localhost');
        expect(response.body.database.port).toBe(5432);
        expect(response.body.database.dialect).toBe('postgres');
      }
    });
  });
});
