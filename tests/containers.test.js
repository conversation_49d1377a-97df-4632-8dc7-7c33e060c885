const request = require('supertest');
const app = require('../server');
const { sequelize, Container, User, Product, InventoryItem } = require('../src/models');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

describe('Container Management API', () => {
  let adminToken, managerToken, employeeToken;
  let adminUser, managerUser, employeeUser;
  let testProduct;

  beforeAll(async () => {
    // Sync database
    await sequelize.sync({ force: true });

    // Create test users
    const hashedPassword = await bcrypt.hash('TestPassword123!', 12);

    adminUser = await User.create({
      username: 'admin',
      email: '<EMAIL>',
      password_hash: hashedPassword,
      role: 'admin',
      is_active: true,
    });

    managerUser = await User.create({
      username: 'manager',
      email: '<EMAIL>',
      password_hash: hashedPassword,
      role: 'manager',
      is_active: true,
    });

    employeeUser = await User.create({
      username: 'employee',
      email: '<EMAIL>',
      password_hash: hashedPassword,
      role: 'employee',
      is_active: true,
    });

    // Generate tokens
    adminToken = jwt.sign(
      { id: adminUser.id, username: adminUser.username, role: adminUser.role },
      process.env.JWT_SECRET,
      { expiresIn: '1h', issuer: 'inventory-manager', audience: 'inventory-manager-users' }
    );

    managerToken = jwt.sign(
      { id: managerUser.id, username: managerUser.username, role: managerUser.role },
      process.env.JWT_SECRET,
      { expiresIn: '1h', issuer: 'inventory-manager', audience: 'inventory-manager-users' }
    );

    employeeToken = jwt.sign(
      { id: employeeUser.id, username: employeeUser.username, role: employeeUser.role },
      process.env.JWT_SECRET,
      { expiresIn: '1h', issuer: 'inventory-manager', audience: 'inventory-manager-users' }
    );

    // Create test product
    testProduct = await Product.create({
      brand: 'Dell',
      model: 'Latitude 7420',
      specifications_json: {
        processor: 'Intel i7',
        ram: '16GB',
        storage: '512GB SSD'
      },
      base_price: 1200.00
    });
  });

  afterAll(async () => {
    await sequelize.close();
  });

  beforeEach(async () => {
    // Clean up containers before each test
    await Container.destroy({ where: {}, force: true });
  });

  describe('POST /api/containers', () => {
    const validContainerData = {
      container_number: 'TEST001',
      arrival_date: '2024-01-15T10:00:00.000Z',
      status: 'arrived',
      total_items: 50
    };

    test('should create container with admin role', async () => {
      const response = await request(app)
        .post('/api/containers')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(validContainerData);

      expect(response.status).toBe(201);
      expect(response.body.message).toBe('Container created successfully');
      expect(response.body.container.container_number).toBe('TEST001');
      expect(response.body.container.status).toBe('arrived');
    });

    test('should create container with manager role', async () => {
      const response = await request(app)
        .post('/api/containers')
        .set('Authorization', `Bearer ${managerToken}`)
        .send(validContainerData);

      expect(response.status).toBe(201);
      expect(response.body.container.container_number).toBe('TEST001');
    });

    test('should reject container creation with employee role', async () => {
      const response = await request(app)
        .post('/api/containers')
        .set('Authorization', `Bearer ${employeeToken}`)
        .send(validContainerData);

      expect(response.status).toBe(403);
      expect(response.body.error).toBe('Forbidden');
    });

    test('should reject container creation without authentication', async () => {
      const response = await request(app)
        .post('/api/containers')
        .send(validContainerData);

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Access denied');
    });

    test('should reject duplicate container number', async () => {
      // Create first container
      await request(app)
        .post('/api/containers')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(validContainerData);

      // Try to create duplicate
      const response = await request(app)
        .post('/api/containers')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(validContainerData);

      expect(response.status).toBe(409);
      expect(response.body.error).toBe('Container already exists');
    });

    test('should validate required fields', async () => {
      const response = await request(app)
        .post('/api/containers')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Validation error');
    });

    test('should validate container number format', async () => {
      const response = await request(app)
        .post('/api/containers')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          ...validContainerData,
          container_number: 'AB' // Too short
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Validation error');
    });

    test('should validate arrival date not in future', async () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);

      const response = await request(app)
        .post('/api/containers')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          ...validContainerData,
          arrival_date: futureDate.toISOString()
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Validation error');
    });
  });

  describe('GET /api/containers', () => {
    beforeEach(async () => {
      // Create test containers
      await Container.bulkCreate([
        {
          container_number: 'TEST001',
          arrival_date: '2024-01-15T10:00:00.000Z',
          status: 'arrived',
          total_items: 50,
          created_by: adminUser.id
        },
        {
          container_number: 'TEST002',
          arrival_date: '2024-01-16T10:00:00.000Z',
          status: 'processing',
          total_items: 30,
          created_by: adminUser.id
        },
        {
          container_number: 'TEST003',
          arrival_date: '2024-01-17T10:00:00.000Z',
          status: 'completed',
          total_items: 25,
          created_by: adminUser.id
        }
      ]);
    });

    test('should get all containers for authenticated user', async () => {
      const response = await request(app)
        .get('/api/containers')
        .set('Authorization', `Bearer ${employeeToken}`);

      expect(response.status).toBe(200);
      expect(response.body.containers).toHaveLength(3);
      expect(response.body.pagination.total_items).toBe(3);
    });

    test('should filter containers by status', async () => {
      const response = await request(app)
        .get('/api/containers?status=processing')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.containers).toHaveLength(1);
      expect(response.body.containers[0].status).toBe('processing');
    });

    test('should filter containers by container number', async () => {
      const response = await request(app)
        .get('/api/containers?container_number=TEST001')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.containers).toHaveLength(1);
      expect(response.body.containers[0].container_number).toBe('TEST001');
    });

    test('should paginate results', async () => {
      const response = await request(app)
        .get('/api/containers?page=1&limit=2')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.containers).toHaveLength(2);
      expect(response.body.pagination.current_page).toBe(1);
      expect(response.body.pagination.per_page).toBe(2);
      expect(response.body.pagination.total_pages).toBe(2);
    });

    test('should reject unauthenticated requests', async () => {
      const response = await request(app)
        .get('/api/containers');

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Access denied');
    });
  });

  describe('GET /api/containers/:id', () => {
    let testContainer;

    beforeEach(async () => {
      testContainer = await Container.create({
        container_number: 'TEST001',
        arrival_date: '2024-01-15T10:00:00.000Z',
        status: 'arrived',
        total_items: 50,
        created_by: adminUser.id
      });
    });

    test('should get container by ID for authenticated user', async () => {
      const response = await request(app)
        .get(`/api/containers/${testContainer.id}`)
        .set('Authorization', `Bearer ${employeeToken}`);

      expect(response.status).toBe(200);
      expect(response.body.container.id).toBe(testContainer.id);
      expect(response.body.container.container_number).toBe('TEST001');
    });

    test('should return 404 for non-existent container', async () => {
      const fakeId = 99999; // Use integer ID since Container uses INTEGER primary key
      const response = await request(app)
        .get(`/api/containers/${fakeId}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Container not found');
    });

    test('should validate ID format', async () => {
      const response = await request(app)
        .get('/api/containers/invalid-id')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Validation error');
    });
  });
});
