const request = require('supertest');
const app = require('../../server');
const { 
  Container, 
  Product, 
  InventoryItem, 
  Customer, 
  SalesOrder, 
  OrderItem,
  User 
} = require('../../src/models');
const jwt = require('jsonwebtoken');

describe('Report Controller', () => {
  let authToken;
  let adminUser;
  let testContainer;
  let testProduct;
  let testCustomer;
  let testInventoryItem;
  let testSalesOrder;

  beforeAll(async () => {
    // Create test admin user
    adminUser = await User.create({
      username: 'testadmin',
      email: '<EMAIL>',
      password_hash: 'hashedpassword',
      role: 'admin',
      is_active: true
    });

    // Generate auth token
    authToken = jwt.sign(
      { 
        id: adminUser.id, 
        username: adminUser.username, 
        role: adminUser.role 
      },
      process.env.JWT_SECRET,
      { 
        expiresIn: '1h',
        issuer: 'inventory-manager',
        audience: 'inventory-manager-users'
      }
    );

    // Create test data
    testContainer = await Container.create({
      container_number: 'TEST-REPORT-001',
      arrival_date: new Date(),
      status: 'completed',
      total_items: 5
    });

    testProduct = await Product.create({
      brand: 'TestBrand',
      model: 'TestModel',
      specifications_json: { cpu: 'Intel i7', ram: '16GB' },
      base_price: 1000.00
    });

    testCustomer = await Customer.create({
      company_name: 'Test Company',
      contact_person: 'Test Contact',
      email: '<EMAIL>',
      credit_limit: 10000.00,
      pricing_tier: 'gold'
    });

    testInventoryItem = await InventoryItem.create({
      product_id: testProduct.id,
      container_id: testContainer.id,
      serial_number: 'TEST-SERIAL-001',
      condition: 'new',
      status: 'available',
      cost_price: 800.00
    });

    testSalesOrder = await SalesOrder.create({
      customer_id: testCustomer.id,
      order_date: new Date(),
      status: 'completed',
      total_amount: 1200.00
    });

    await OrderItem.create({
      order_id: testSalesOrder.id,
      inventory_item_id: testInventoryItem.id,
      quantity: 1,
      unit_price: 1200.00
    });
  });

  afterAll(async () => {
    // Clean up test data
    await OrderItem.destroy({ where: {}, force: true });
    await SalesOrder.destroy({ where: {}, force: true });
    await InventoryItem.destroy({ where: {}, force: true });
    await Customer.destroy({ where: {}, force: true });
    await Product.destroy({ where: {}, force: true });
    await Container.destroy({ where: {}, force: true });
    await User.destroy({ where: {}, force: true });
  });

  describe('GET /api/reports/inventory', () => {
    it('should return inventory report for admin user', async () => {
      const response = await request(app)
        .get('/api/reports/inventory')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('stockLevels');
      expect(response.body.data).toHaveProperty('agingAnalysis');
      expect(response.body.data).toHaveProperty('lowStockAlerts');
      expect(response.body.data).toHaveProperty('turnoverAnalysis');
      expect(response.body.data).toHaveProperty('summary');
      expect(response.body.data).toHaveProperty('pagination');
      expect(response.body.data.metadata.reportType).toBe('inventory');
    });

    it('should return inventory report with date filtering', async () => {
      const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      const endDate = new Date().toISOString().split('T')[0];

      const response = await request(app)
        .get('/api/reports/inventory')
        .query({ startDate, endDate })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.metadata.dateRange.startDate).toBe(startDate);
      expect(response.body.data.metadata.dateRange.endDate).toBe(endDate);
    });

    it('should return CSV format when requested', async () => {
      const response = await request(app)
        .get('/api/reports/inventory')
        .query({ format: 'csv' })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.headers['content-type']).toContain('text/csv');
      expect(response.headers['content-disposition']).toContain('attachment');
    });

    it('should deny access to employee role', async () => {
      const employeeUser = await User.create({
        username: 'employee',
        email: '<EMAIL>',
        password_hash: 'hashedpassword',
        role: 'employee',
        is_active: true
      });

      const employeeToken = jwt.sign(
        { id: employeeUser.id, username: employeeUser.username, role: employeeUser.role },
        process.env.JWT_SECRET,
        { expiresIn: '1h', issuer: 'inventory-manager', audience: 'inventory-manager-users' }
      );

      await request(app)
        .get('/api/reports/inventory')
        .set('Authorization', `Bearer ${employeeToken}`)
        .expect(403);

      await User.destroy({ where: { id: employeeUser.id }, force: true });
    });
  });

  describe('GET /api/reports/sales', () => {
    it('should return sales report for admin user', async () => {
      const response = await request(app)
        .get('/api/reports/sales')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('overview');
      expect(response.body.data).toHaveProperty('trends');
      expect(response.body.data).toHaveProperty('topProducts');
      expect(response.body.data).toHaveProperty('revenueByTier');
      expect(response.body.data).toHaveProperty('recentOrders');
      expect(response.body.data.metadata.reportType).toBe('sales');
    });

    it('should support period parameter for trends', async () => {
      const response = await request(app)
        .get('/api/reports/sales')
        .query({ period: 'week' })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.metadata.filters.period).toBe('week');
    });
  });

  describe('GET /api/reports/containers', () => {
    it('should return container report for admin user', async () => {
      const response = await request(app)
        .get('/api/reports/containers')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('containers');
      expect(response.body.data).toHaveProperty('statusDistribution');
      expect(response.body.data).toHaveProperty('efficiencyTrends');
      expect(response.body.data).toHaveProperty('summary');
      expect(response.body.data.metadata.reportType).toBe('containers');
    });
  });

  describe('GET /api/reports/customers', () => {
    it('should return customer report for admin user', async () => {
      const response = await request(app)
        .get('/api/reports/customers')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('customers');
      expect(response.body.data).toHaveProperty('tierAnalysis');
      expect(response.body.data).toHaveProperty('creditAnalysis');
      expect(response.body.data).toHaveProperty('summary');
      expect(response.body.data.metadata.reportType).toBe('customers');
    });
  });

  describe('GET /api/reports/dashboard', () => {
    it('should return dashboard report for admin user', async () => {
      const response = await request(app)
        .get('/api/reports/dashboard')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('overview');
      expect(response.body.data).toHaveProperty('charts');
      expect(response.body.data).toHaveProperty('alerts');
      expect(response.body.data).toHaveProperty('recentActivity');
      expect(response.body.data.metadata.reportType).toBe('dashboard');
      expect(response.body.data.metadata.userRole).toBe('admin');
    });

    it('should return limited dashboard for employee user', async () => {
      const employeeUser = await User.create({
        username: 'employee2',
        email: '<EMAIL>',
        password_hash: 'hashedpassword',
        role: 'employee',
        is_active: true
      });

      const employeeToken = jwt.sign(
        { id: employeeUser.id, username: employeeUser.username, role: employeeUser.role },
        process.env.JWT_SECRET,
        { expiresIn: '1h', issuer: 'inventory-manager', audience: 'inventory-manager-users' }
      );

      const response = await request(app)
        .get('/api/reports/dashboard')
        .set('Authorization', `Bearer ${employeeToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.metadata.userRole).toBe('employee');
      // Employee should have limited access - no sales/customer data
      expect(response.body.data.overview).not.toHaveProperty('sales');

      await User.destroy({ where: { id: employeeUser.id }, force: true });
    });
  });

  describe('Rate Limiting', () => {
    it('should enforce rate limiting on report endpoints', async () => {
      // Make multiple requests quickly to trigger rate limiting
      const requests = Array(12).fill().map(() => 
        request(app)
          .get('/api/reports/dashboard')
          .set('Authorization', `Bearer ${authToken}`)
      );

      const responses = await Promise.all(requests);
      
      // Some requests should be rate limited (429 status)
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('Authentication', () => {
    it('should require authentication for all report endpoints', async () => {
      await request(app)
        .get('/api/reports/inventory')
        .expect(401);

      await request(app)
        .get('/api/reports/sales')
        .expect(401);

      await request(app)
        .get('/api/reports/containers')
        .expect(401);

      await request(app)
        .get('/api/reports/customers')
        .expect(401);

      await request(app)
        .get('/api/reports/dashboard')
        .expect(401);
    });
  });
});
